#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de vérification de complétude du fichier Descriptif.txt
"""

import os
import re

def count_methods_in_subdirs():
    """Compte les méthodes dans chaque sous-dossier."""
    mapping = {
        'CalculConfiance': 'Section 1',
        'EvaluationMetriques': 'Section 2', 
        'GestionDonnees': 'Section 3',
        'InterfaceUtilisateur': 'Section 4',
        'OptimisationEntrainement': 'Section 5',
        'ReseauxNeuronaux': 'Section 6',
        'UtilitairesFonctions': 'Section 7',
        'anciennesclasses': 'Section 8'
    }
    
    print('VÉRIFICATION CORRESPONDANCE SOUS-DOSSIERS vs FICHIER PRINCIPAL:')
    print('=' * 70)
    
    subdir_counts = {}
    
    for folder, section in mapping.items():
        descriptif_path = os.path.join(folder, 'Descriptif.txt')
        if os.path.exists(descriptif_path):
            with open(descriptif_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Compter les méthodes
            methods = re.findall(r'^\d+\.\s+\S+\.txt', content, re.MULTILINE)
            count = len(methods)
            subdir_counts[section] = count
            print(f'{folder:25} -> {section}: {count:2d} méthodes')
        else:
            print(f'{folder:25} -> {section}: FICHIER MANQUANT')
            subdir_counts[section] = 0
    
    return subdir_counts

def analyze_main_file():
    """Analyse le fichier principal."""
    print('\nVÉRIFICATION FICHIER PRINCIPAL:')
    print('=' * 50)
    
    with open('Descriptif.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Trouver toutes les sections
    sections = re.findall(r'SECTION (\d+) : ([^=]+)', content)
    
    main_counts = {}
    
    for i, (num, name) in enumerate(sections):
        section_start = content.find(f'SECTION {num} : {name}')
        
        # Trouver la fin de la section
        if i < len(sections) - 1:
            next_num, next_name = sections[i + 1]
            section_end = content.find(f'SECTION {next_num} : {next_name}')
        else:
            section_end = len(content)
        
        section_content = content[section_start:section_end]
        
        # Compter les méthodes
        methods = re.findall(r'^\d+\.\s+\S+\.txt', section_content, re.MULTILINE)
        count = len(methods)
        section_key = f'Section {num}'
        main_counts[section_key] = count
        
        print(f'Section {num} ({name.strip()}): {count} méthodes')
    
    # Total
    total_methods = len(re.findall(r'^\d+\.\s+\S+\.txt', content, re.MULTILINE))
    print(f'\nTOTAL MÉTHODES DANS LE FICHIER: {total_methods}')
    
    # Vérifier l'en-tête
    header_section = content[:content.find('SECTION 1')]
    total_match = re.search(r'TOTAL : (\d+) MÉTHODES', header_section)
    if total_match:
        announced_total = int(total_match.group(1))
        print(f'Total annoncé dans en-tête: {announced_total}')
        if announced_total == total_methods:
            print('✅ Correspondance en-tête OK')
        else:
            print('❌ Incohérence en-tête')
    
    return main_counts, total_methods

def compare_counts(subdir_counts, main_counts):
    """Compare les comptages."""
    print('\nCOMPARAISON SOUS-DOSSIERS vs FICHIER PRINCIPAL:')
    print('=' * 60)
    
    all_match = True
    
    for section in sorted(subdir_counts.keys()):
        subdir_count = subdir_counts.get(section, 0)
        main_count = main_counts.get(section, 0)
        
        status = '✅' if subdir_count == main_count else '❌'
        if subdir_count != main_count:
            all_match = False
        
        print(f'{section}: Sous-dossier={subdir_count:2d}, Principal={main_count:2d} {status}')
    
    print(f'\nRÉSULTAT GLOBAL: {"✅ COMPLET" if all_match else "❌ INCOMPLET"}')
    
    return all_match

if __name__ == "__main__":
    subdir_counts = count_methods_in_subdirs()
    main_counts, total_methods = analyze_main_file()
    is_complete = compare_counts(subdir_counts, main_counts)
    
    if is_complete:
        print('\n🎉 LE FICHIER PRINCIPAL EST COMPLET ET COHÉRENT !')
    else:
        print('\n⚠️  LE FICHIER PRINCIPAL NÉCESSITE DES CORRECTIONS')
