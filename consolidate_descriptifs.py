#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de consolidation des descriptifs détaillés
Copie tous les descriptifs des sous-dossiers vers le fichier principal Descriptif.txt
en respectant la structure et la numérotation continue.
"""

import os
import re
from pathlib import Path

def read_file_content(file_path):
    """Lit le contenu d'un fichier avec gestion d'encodage."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        with open(file_path, 'r', encoding='latin-1') as f:
            return f.read()
    except Exception as e:
        print(f"Erreur lecture {file_path}: {e}")
        return ""

def extract_methods_from_descriptif(content):
    """Extrait les méthodes détaillées d'un fichier Descriptif.txt de sous-dossier."""
    methods = []

    # Pattern pour identifier le début d'une méthode (numéro. nom_fichier.txt)
    method_pattern = r'^\d+\.\s+\S+\.txt\s+\('

    lines = content.split('\n')
    current_method = []
    in_method = False

    i = 0
    while i < len(lines):
        line = lines[i]

        # Vérifie si c'est le début d'une nouvelle méthode
        if re.match(method_pattern, line.strip()):
            # Sauvegarde la méthode précédente si elle existe
            if current_method:
                # Nettoie les lignes vides à la fin
                while current_method and current_method[-1].strip() == "":
                    current_method.pop()
                if current_method:
                    methods.append('\n'.join(current_method))

            # Commence une nouvelle méthode
            current_method = [line]
            in_method = True

        elif in_method:
            # Continue à collecter les lignes de la méthode courante
            current_method.append(line)

            # Vérifie si la prochaine ligne non-vide est une nouvelle méthode
            if line.strip() == "":
                # Regarde les prochaines lignes pour voir si c'est une nouvelle méthode
                j = i + 1
                while j < len(lines) and lines[j].strip() == "":
                    j += 1

                if j < len(lines) and re.match(method_pattern, lines[j].strip()):
                    # On a trouvé le début de la méthode suivante, on s'arrête ici
                    # Nettoie les lignes vides à la fin
                    while current_method and current_method[-1].strip() == "":
                        current_method.pop()
                    if current_method:
                        methods.append('\n'.join(current_method))
                    current_method = []
                    in_method = False

        i += 1

    # Ajoute la dernière méthode
    if current_method:
        # Nettoie les lignes vides à la fin
        while current_method and current_method[-1].strip() == "":
            current_method.pop()
        if current_method:
            methods.append('\n'.join(current_method))

    return methods

def create_section_mapping():
    """Définit le mapping entre sous-dossiers et sections du fichier principal."""
    return {
        'CalculConfiance': 'SECTION 1 : CALCUL CONFIANCE',
        'EvaluationMetriques': 'SECTION 2 : EVALUATION METRIQUES',
        'GestionDonnees': 'SECTION 3 : GESTION DONNEES',
        'InterfaceUtilisateur': 'SECTION 4 : INTERFACE UTILISATEUR',
        'OptimisationEntrainement': 'SECTION 5 : OPTIMISATION ENTRAINEMENT',
        'ReseauxNeuronaux': 'SECTION 6 : RESEAUX NEURONAUX',
        'UtilitairesFonctions': 'SECTION 7 : UTILITAIRES FONCTIONS',
        'anciennesclasses': 'SECTION 8 : ANCIENNES CLASSES'
    }

def renumber_methods(methods, start_number=1):
    """Renumérote les méthodes en commençant par start_number."""
    renumbered = []

    for i, method in enumerate(methods):
        lines = method.split('\n')
        if lines and lines[0].strip():
            # Remplace le numéro au début de la première ligne
            first_line = lines[0]
            new_first_line = re.sub(r'^\d+\.', f'{start_number + i}.', first_line)
            lines[0] = new_first_line

        renumbered.append('\n'.join(lines))

    return renumbered

def consolidate_descriptifs():
    """Fonction principale de consolidation."""
    print("🚀 Début de la consolidation des descriptifs...")

    # Chemins
    main_file = Path('Descriptif.txt')
    section_mapping = create_section_mapping()

    # Lit le fichier principal actuel
    if not main_file.exists():
        print("❌ Fichier Descriptif.txt principal non trouvé!")
        return False

    main_content = read_file_content(main_file)

    # Extrait l'en-tête jusqu'à la première section
    header_end = main_content.find('================================================================================\nSECTION 1 : CALCUL CONFIANCE')
    if header_end == -1:
        # Essaie une autre variante
        header_end = main_content.find('SECTION 1 : CALCUL CONFIANCE')
        if header_end != -1:
            # Recule pour inclure la ligne de séparation
            header_end = main_content.rfind('================================================================================', 0, header_end)

    if header_end == -1:
        print("❌ Structure du fichier principal non reconnue!")
        return False

    header = main_content[:header_end]

    # Construit le nouveau contenu
    new_content = header
    total_methods = 0

    # Pour chaque section
    for folder_name, section_title in section_mapping.items():
        print(f"📁 Traitement du dossier: {folder_name}")

        # Chemin vers le descriptif du sous-dossier
        descriptif_path = Path(folder_name) / 'Descriptif.txt'

        if not descriptif_path.exists():
            print(f"⚠️  Fichier {descriptif_path} non trouvé, section ignorée")
            continue

        # Lit le contenu du sous-dossier
        folder_content = read_file_content(descriptif_path)

        # Extrait les méthodes détaillées
        methods = extract_methods_from_descriptif(folder_content)

        if not methods:
            print(f"⚠️  Aucune méthode trouvée dans {folder_name}")
            continue

        # Renumérote les méthodes
        renumbered_methods = renumber_methods(methods)

        # Ajoute la section au contenu principal
        new_content += "================================================================================\n"
        new_content += f"{section_title}\n"
        new_content += "================================================================================\n\n"

        # Ajoute toutes les méthodes renumerotées
        for i, method in enumerate(renumbered_methods):
            new_content += method
            # Ajoute une ligne vide entre les méthodes, sauf pour la dernière
            if i < len(renumbered_methods) - 1:
                new_content += "\n\n"
            else:
                new_content += "\n"

        # Ajoute une ligne vide après la section
        new_content += "\n"

        total_methods += len(methods)
        print(f"✅ {len(methods)} méthodes ajoutées pour {section_title}")

    # Sauvegarde le nouveau fichier
    try:
        # Crée une sauvegarde du fichier original
        backup_path = Path('Descriptif_backup.txt')
        if main_file.exists():
            import shutil
            shutil.copy2(main_file, backup_path)
            print(f"💾 Sauvegarde créée: {backup_path}")

        with open('Descriptif.txt', 'w', encoding='utf-8') as f:
            f.write(new_content)

        print("🎉 Consolidation terminée avec succès!")
        print(f"📄 Fichier Descriptif.txt mis à jour avec {total_methods} méthodes détaillées")
        print(f"📊 Lignes totales: {len(new_content.split(chr(10)))}")
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde: {e}")
        return False

if __name__ == "__main__":
    success = consolidate_descriptifs()
    if success:
        print("\n🏆 MISSION ACCOMPLIE : Plateforme Professionnelle Complète!")
        print("📊 Tous les descriptifs détaillés ont été consolidés avec succès.")
    else:
        print("\n❌ Échec de la consolidation. Vérifiez les erreurs ci-dessus.")
