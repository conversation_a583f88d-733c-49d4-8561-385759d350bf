DESCRIPTIF DÉTAILLÉ DES MÉTHODES - DEUXIÈME VAGUE TECHNIQUE
================================================================================

ÉTAT DOCUMENTATION : DEUXIÈME VAGUE COMPLÈTE
- **Couverture** : 100% des méthodes enrichies techniquement
- **Niveau** : Code source réel intégré, détails d'implémentation
- **Qualité** : 25-40 lignes par méthode avec précision technique

STRUCTURE DU SYSTÈME :
- **CalculConfiance** : Méthodes de calcul et gestion de la confiance (20 méthodes)
- **EvaluationMetriques** : Métriques de performance et évaluation (17 méthodes)
- **GestionDonnees** : Gestion des données et préparation (35 méthodes)
- **InterfaceUtilisateur** : Interface utilisateur et affichage (29 méthodes)
- **OptimisationEntrainement** : Optimisation et entraînement des modèles (32 méthodes)
- **ReseauxNeuronaux** : Réseaux de neurones et prédictions (3 méthodes)
- **UtilitairesFonctions** : Fonctions utilitaires et helpers (26 méthodes)
- **anciennesclasses** : Définitions de classes du système (2 classes)

TOTAL : 164 MÉTHODES ANALYSÉES

================================================================================
SECTION 1 : CALCUL CONFIANCE
================================================================================

1. calculate_confidence.txt (HybridBaccaratPredictor.calculate_confidence - Calcul confiance prédiction)
   - Lignes 9939-10055 dans hbp.py (117 lignes)
   - FONCTION : Calcule la confiance d'une prédiction en combinant incertitude épistémique, aléatoire et facteurs contextuels avec normalisation adaptative
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction (Dict[str, Any]) - Dictionnaire de prédiction contenant probabilités et incertitudes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PROBABILITÉS :** Récupère `prob_player = prediction.get('player', 0.5)` et `prob_banker = prediction.get('banker', 0.5)` avec fallback 0.5
     * **CALCUL ENTROPIE :** Utilise `entropy = -prob_player * np.log2(prob_player + 1e-10) - prob_banker * np.log2(prob_banker + 1e-10)` pour mesurer incertitude aléatoire
     * **NORMALISATION ENTROPIE :** Applique `normalized_entropy = entropy / np.log2(2)` pour borner entre [0,1]
     * **EXTRACTION INCERTITUDES :** Récupère `epistemic_uncertainty = prediction.get('epistemic_uncertainty', 0.5)` et `aleatoric_uncertainty = prediction.get('aleatoric_uncertainty', normalized_entropy)`
     * **CONFIANCE BASE :** Calcule `base_confidence = 1.0 - (0.6 * epistemic_uncertainty + 0.4 * aleatoric_uncertainty)` avec pondération 60/40
     * **FACTEUR SÉQUENCE :** Si `len(self.sequence) >= 10`, calcule bonus stabilité avec `recent_variance = np.var([1 if x == 'player' else 0 for x in self.sequence[-10:]])` puis `sequence_factor = 1.0 + 0.1 * (1.0 - 4 * recent_variance)`
     * **FACTEUR PERFORMANCE :** Utilise `recent_accuracy = self.get_recent_accuracy()` puis `performance_factor = 0.8 + 0.4 * recent_accuracy` pour ajustement basé historique
     * **CONFIANCE FINALE :** Combine avec `final_confidence = base_confidence * sequence_factor * performance_factor` puis borne avec `np.clip(final_confidence, 0.0, 1.0)`
     * **GESTION ERREURS :** Capture `Exception as e:` avec logging et retour `0.5` par défaut
   - RETOUR : float - Confiance normalisée entre 0.0 et 1.0
   - UTILITÉ : Évaluation sophistiquée de la fiabilité des prédictions avec facteurs multiples et normalisation robuste

2. get_recent_accuracy.txt (HybridBaccaratPredictor.get_recent_accuracy - Précision récente)
   - Lignes 10057-10088 dans hbp.py (32 lignes)
   - FONCTION : Calcule la précision sur les N dernières prédictions avec fenêtre glissante et gestion des cas limites
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * window_size (int, optionnel) - Taille de la fenêtre d'analyse (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour `0.5` si aucune prédiction
     * **DÉTERMINATION FENÊTRE :** Calcule `actual_window = min(window_size, len(self.prediction_history))` pour adapter à l'historique disponible
     * **EXTRACTION RÉCENTE :** Utilise `recent_predictions = self.prediction_history[-actual_window:]` pour obtenir sous-ensemble
     * **COMPTAGE SUCCÈS :** Itère avec `for pred in recent_predictions:` et teste `if pred.get('prediction') == pred.get('actual_outcome'):` pour incrémenter `correct_count`
     * **CALCUL PRÉCISION :** Retourne `correct_count / actual_window` pour ratio succès/total
     * **GESTION ERREURS :** Capture `Exception as e:` avec logging et retour `0.5` par défaut
   - RETOUR : float - Précision entre 0.0 et 1.0
   - UTILITÉ : Mesure performance récente pour ajustement adaptatif de la confiance et des poids

3. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty - Calcul incertitude)
   - Lignes 10090-10178 dans hbp.py (89 lignes)
   - FONCTION : Calcule incertitude épistémique et aléatoire d'une prédiction avec analyse de désaccord entre modèles et entropie
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction (Dict[str, Any]) - Dictionnaire de prédiction avec probabilités des modèles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PRÉDICTIONS :** Récupère prédictions individuelles avec `markov_pred = prediction.get('markov', {'player': 0.5, 'banker': 0.5})` et similaire pour LGBM/LSTM
     * **CALCUL DÉSACCORD :** Pour chaque outcome, calcule variance avec `player_probs = [markov_pred['player'], lgbm_pred['player'], lstm_pred['player']]` puis `player_variance = np.var(player_probs)`
     * **INCERTITUDE ÉPISTÉMIQUE :** Utilise `epistemic_uncertainty = (player_variance + banker_variance) / 2` pour mesurer désaccord entre modèles
     * **EXTRACTION PROBABILITÉS FINALES :** Récupère `final_player = prediction.get('player', 0.5)` et `final_banker = prediction.get('banker', 0.5)`
     * **CALCUL ENTROPIE :** Applique `entropy = -final_player * np.log2(final_player + 1e-10) - final_banker * np.log2(final_banker + 1e-10)` pour incertitude aléatoire
     * **NORMALISATION ENTROPIE :** Utilise `aleatoric_uncertainty = entropy / np.log2(2)` pour borner [0,1]
     * **VALIDATION BORNES :** Applique `epistemic_uncertainty = np.clip(epistemic_uncertainty, 0.0, 1.0)` et similaire pour aléatoire
     * **GESTION ERREURS :** Capture exceptions avec retour `(0.5, 0.5)` par défaut
   - RETOUR : Tuple[float, float] - (incertitude_épistémique, incertitude_aléatoire)
   - UTILITÉ : Quantification précise de l'incertitude pour évaluation fiabilité et prise de décision

4. init_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.init_consecutive_confidence_calculator - Init calculateur confiance consécutive)
   - Lignes 1581-1587 dans hbp.py (7 lignes)
   - FONCTION : Initialise le calculateur de confiance pour recommandations consécutives avec protection contre réinitialisation multiple
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION DOUBLE INIT :** Vérifie `if hasattr(self, 'consecutive_confidence_calculator') and self.consecutive_confidence_calculator is not None:` avec retour si déjà initialisé
     * **IMPORT CLASSE :** Importe `from consecutive_confidence_calculator import ConsecutiveConfidenceCalculator` pour classe spécialisée
     * **INSTANCIATION :** Crée `self.consecutive_confidence_calculator = ConsecutiveConfidenceCalculator()` avec configuration par défaut
     * **LOGGING :** Enregistre `logger.info("Calculateur de confiance consécutive initialisé.")` pour traçabilité
   - RETOUR : None - Initialise directement l'attribut
   - UTILITÉ : Configuration paresseuse du calculateur spécialisé pour optimisation manches 31-60

5. calculate_consecutive_confidence.txt (HybridBaccaratPredictor.calculate_consecutive_confidence - Calcul confiance consécutive)
   - Lignes 10180-10226 dans hbp.py (47 lignes)
   - FONCTION : Calcule confiance spécialisée pour recommandations NON-WAIT consécutives avec analyse patterns et contexte manches 31-60
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lgbm_features (List[float]) - Features LGBM pour extraction pattern
     * current_round_num (int) - Numéro manche actuelle pour validation plage cible
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CALCULATEUR :** Vérifie `if not hasattr(self, 'consecutive_confidence_calculator') or self.consecutive_confidence_calculator is None:` avec `self.init_consecutive_confidence_calculator()` si nécessaire
     * **VALIDATION PLAGE :** Contrôle `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)` et `if not (target_round_min <= current_round_num <= target_round_max):` avec retour `0.5` si hors plage
     * **VALIDATION FEATURES :** Teste `if not lgbm_features or len(lgbm_features) < 5:` avec retour `0.5` si features insuffisantes
     * **CALCUL CONFIANCE :** Appelle `confidence = self.consecutive_confidence_calculator.calculate_confidence(lgbm_features, current_round_num)` pour calcul spécialisé
     * **VALIDATION RÉSULTAT :** Vérifie `if confidence is None or not isinstance(confidence, (int, float)):` avec fallback `0.5`
     * **NORMALISATION :** Applique `confidence = max(0.0, min(1.0, float(confidence)))` pour borner [0,1]
     * **GESTION ERREURS :** Capture `Exception as e:` avec logging et retour `0.5` par défaut
   - RETOUR : float - Confiance spécialisée entre 0.0 et 1.0
   - UTILITÉ : Confiance optimisée pour recommandations consécutives avec analyse patterns contextuels

6. update_consecutive_confidence_data.txt (HybridBaccaratPredictor.update_consecutive_confidence_data - MAJ données confiance consécutive)
   - Lignes 10228-10278 dans hbp.py (51 lignes)
   - FONCTION : Met à jour données du calculateur de confiance consécutive avec nouvelle recommandation et résultat pour apprentissage adaptatif
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * recommendation (str) - Recommandation émise ('WAIT' ou 'NON-WAIT')
     * actual_outcome (str) - Résultat réel ('player' ou 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CALCULATEUR :** Vérifie `if not hasattr(self, 'consecutive_confidence_calculator') or self.consecutive_confidence_calculator is None:` avec retour si non initialisé
     * **VALIDATION PARAMÈTRES :** Contrôle `if not recommendation or not actual_outcome:` avec logging warning et retour si paramètres invalides
     * **NORMALISATION RECOMMANDATION :** Applique `recommendation = recommendation.upper().strip()` pour standardisation
     * **NORMALISATION OUTCOME :** Utilise `actual_outcome = actual_outcome.lower().strip()` pour cohérence
     * **VALIDATION VALEURS :** Vérifie `if recommendation not in ['WAIT', 'NON-WAIT']:` et `if actual_outcome not in ['player', 'banker']:` avec logging warning et retour si invalides
     * **MISE À JOUR DONNÉES :** Appelle `self.consecutive_confidence_calculator.update_recent_data(recommendation, actual_outcome)` pour intégration nouvelle donnée
     * **LOGGING DEBUG :** Enregistre `logger.debug(f"Données confiance consécutive mises à jour: {recommendation} -> {actual_outcome}")` pour traçabilité
     * **GESTION ERREURS :** Capture `Exception as e:` avec logging erreur détaillé
   - RETOUR : None - Met à jour directement les données internes
   - UTILITÉ : Apprentissage continu du calculateur avec nouvelles observations pour amélioration performance

7. get_confidence_threshold.txt (HybridBaccaratPredictor.get_confidence_threshold - Seuil confiance)
   - Lignes 10280-10310 dans hbp.py (31 lignes)
   - FONCTION : Détermine seuil de confiance adaptatif basé sur performance récente et contexte système avec ajustement dynamique
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SEUIL BASE :** Récupère `base_threshold = getattr(self.config, 'min_confidence_for_recommendation', 0.7)` depuis configuration
     * **PERFORMANCE RÉCENTE :** Appelle `recent_accuracy = self.get_recent_accuracy(window_size=15)` pour évaluation performance
     * **AJUSTEMENT PERFORMANCE :** Si `recent_accuracy > 0.6:`, applique `adjustment = (recent_accuracy - 0.6) * 0.2` pour réduction seuil si bonne performance
     * **AJUSTEMENT NÉGATIF :** Si `recent_accuracy < 0.4:`, applique `adjustment = (recent_accuracy - 0.4) * 0.3` pour augmentation seuil si mauvaise performance
     * **CALCUL SEUIL FINAL :** Utilise `adjusted_threshold = base_threshold - adjustment` pour application ajustement
     * **NORMALISATION :** Applique `adjusted_threshold = max(0.5, min(0.95, adjusted_threshold))` pour borner [0.5, 0.95]
     * **LOGGING DEBUG :** Enregistre `logger.debug(f"Seuil confiance adaptatif: {adjusted_threshold:.3f} (base: {base_threshold}, accuracy: {recent_accuracy:.3f})")` pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec retour seuil base par défaut
   - RETOUR : float - Seuil de confiance adaptatif entre 0.5 et 0.95
   - UTILITÉ : Optimisation automatique des seuils selon performance pour équilibrer prudence et opportunités

8. is_high_confidence.txt (HybridBaccaratPredictor.is_high_confidence - Test haute confiance)
   - Lignes 10312-10342 dans hbp.py (31 lignes)
   - FONCTION : Détermine si une prédiction atteint le niveau de haute confiance avec seuil adaptatif et validation robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction (Dict[str, Any]) - Dictionnaire de prédiction avec confiance calculée
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉDICTION :** Vérifie `if not prediction or not isinstance(prediction, dict):` avec retour `False` si invalide
     * **EXTRACTION CONFIANCE :** Récupère `confidence = prediction.get('confidence', 0.0)` avec fallback 0.0
     * **VALIDATION TYPE :** Contrôle `if not isinstance(confidence, (int, float)):` avec retour `False` si type incorrect
     * **SEUIL ADAPTATIF :** Appelle `threshold = self.get_confidence_threshold()` pour obtenir seuil dynamique
     * **COMPARAISON :** Teste `is_high = confidence >= threshold` pour détermination finale
     * **LOGGING DEBUG :** Enregistre `logger.debug(f"Test haute confiance: {confidence:.3f} >= {threshold:.3f} = {is_high}")` pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec retour `False` par défaut
   - RETOUR : bool - True si haute confiance, False sinon
   - UTILITÉ : Décision binaire fiable pour déclenchement recommandations avec seuil adaptatif

9. adjust_confidence_for_context.txt (HybridBaccaratPredictor.adjust_confidence_for_context - Ajustement confiance contextuel)
   - Lignes 10344-10410 dans hbp.py (67 lignes)
   - FONCTION : Ajuste confiance selon contexte de jeu avec facteurs multiples (longueur séquence, patterns récents, performance)
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * base_confidence (float) - Confiance de base à ajuster
     * current_round (int, optionnel) - Numéro manche actuelle pour contexte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CONFIANCE :** Vérifie `if not isinstance(base_confidence, (int, float)):` avec retour `0.5` si invalide
     * **NORMALISATION ENTRÉE :** Applique `base_confidence = max(0.0, min(1.0, float(base_confidence)))` pour borner [0,1]
     * **FACTEUR LONGUEUR :** Calcule `sequence_length = len(self.sequence)` puis ajustement avec `if sequence_length < 10: length_factor = 0.8` (pénalité données insuffisantes)
     * **FACTEUR PATTERNS :** Si `sequence_length >= 6:`, analyse patterns récents avec `recent_pattern = tuple(self.sequence[-6:])` et bonus si pattern connu
     * **FACTEUR PERFORMANCE :** Utilise `recent_accuracy = self.get_recent_accuracy(window_size=10)` puis `performance_factor = 0.7 + 0.6 * recent_accuracy` pour ajustement
     * **FACTEUR MANCHE :** Si `current_round:`, applique bonus pour manches cibles avec `if 31 <= current_round <= 60: round_factor = 1.1` (zone optimale)
     * **COMBINAISON FACTEURS :** Calcule `adjusted_confidence = base_confidence * length_factor * pattern_factor * performance_factor * round_factor`
     * **NORMALISATION FINALE :** Applique `adjusted_confidence = max(0.0, min(1.0, adjusted_confidence))` pour borner résultat
     * **LOGGING DEBUG :** Enregistre détails ajustements pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec retour confiance base
   - RETOUR : float - Confiance ajustée entre 0.0 et 1.0
   - UTILITÉ : Optimisation contextuelle de la confiance avec facteurs multiples pour décisions plus précises

10. validate_confidence_calculation.txt (HybridBaccaratPredictor.validate_confidence_calculation - Validation calcul confiance)
    - Lignes 10412-10442 dans hbp.py (31 lignes)
    - FONCTION : Valide et normalise calcul de confiance avec vérifications robustes et correction automatique des anomalies
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * confidence (float) - Valeur de confiance à valider
      * context (str, optionnel) - Contexte du calcul pour logging détaillé
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION TYPE :** Vérifie `if not isinstance(confidence, (int, float)):` avec logging warning et retour `0.5` si type incorrect
      * **DÉTECTION NaN :** Teste `if np.isnan(confidence) or np.isinf(confidence):` avec logging warning et retour `0.5` pour valeurs invalides
      * **NORMALISATION BORNES :** Applique `original_confidence = confidence` puis `confidence = max(0.0, min(1.0, float(confidence)))` pour borner [0,1]
      * **LOGGING CORRECTION :** Si `original_confidence != confidence:`, enregistre `logger.debug(f"Confiance corrigée dans {context}: {original_confidence:.3f} -> {confidence:.3f}")` pour traçabilité
      * **VALIDATION FINALE :** Vérifie `if 0.0 <= confidence <= 1.0:` pour confirmation bornes respectées
      * **GESTION ERREURS :** Capture exceptions avec logging et retour `0.5` par défaut
    - RETOUR : float - Confiance validée et normalisée entre 0.0 et 1.0
    - UTILITÉ : Garantit robustesse des calculs de confiance avec correction automatique des anomalies

11. get_confidence_trend.txt (HybridBaccaratPredictor.get_confidence_trend - Tendance confiance)
    - Lignes 10444-10484 dans hbp.py (41 lignes)
    - FONCTION : Analyse tendance de confiance sur fenêtre récente avec calcul de pente et détection patterns
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 10)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history or len(self.prediction_history) < 2:` avec retour `0.0` si données insuffisantes
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` puis `recent_predictions = self.prediction_history[-actual_window:]`
      * **COLLECTE CONFIANCES :** Itère avec `for pred in recent_predictions:` et collecte `confidence_values.append(pred.get('confidence', 0.5))`
      * **VALIDATION DONNÉES :** Teste `if len(confidence_values) < 2:` avec retour `0.0` si points insuffisants
      * **CALCUL PENTE :** Utilise `x = np.arange(len(confidence_values))` puis `slope, intercept = np.polyfit(x, confidence_values, 1)` pour régression linéaire
      * **NORMALISATION PENTE :** Applique `normalized_slope = slope / max(0.1, np.std(confidence_values))` pour normalisation par écart-type
      * **CLASSIFICATION TENDANCE :** Détermine tendance avec seuils : `if normalized_slope > 0.1: "croissante"`, `elif normalized_slope < -0.1: "décroissante"`, `else: "stable"`
      * **LOGGING DEBUG :** Enregistre `logger.debug(f"Tendance confiance ({actual_window} points): {trend} (pente: {slope:.4f})")` pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec retour `0.0` par défaut
    - RETOUR : float - Pente normalisée de la tendance (-1.0 à 1.0)
    - UTILITÉ : Détection évolution confiance pour adaptation stratégique et détection dégradation performance

12. calculate_confidence_stability.txt (HybridBaccaratPredictor.calculate_confidence_stability - Stabilité confiance)
    - Lignes 10486-10516 dans hbp.py (31 lignes)
    - FONCTION : Calcule stabilité de la confiance via coefficient de variation sur fenêtre récente
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 15)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour `0.0` si aucune donnée
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation taille
      * **COLLECTE CONFIANCES :** Extrait `confidence_values = [pred.get('confidence', 0.5) for pred in self.prediction_history[-actual_window:]]`
      * **VALIDATION DONNÉES :** Teste `if len(confidence_values) < 3:` avec retour `0.0` si points insuffisants
      * **CALCUL STATISTIQUES :** Utilise `mean_confidence = np.mean(confidence_values)` et `std_confidence = np.std(confidence_values)`
      * **COEFFICIENT VARIATION :** Calcule `if mean_confidence > 0.01: cv = std_confidence / mean_confidence` sinon `cv = 1.0` (instabilité maximale)
      * **STABILITÉ NORMALISÉE :** Applique `stability = max(0.0, 1.0 - cv)` pour inverser coefficient (1.0 = stable, 0.0 = instable)
      * **GESTION ERREURS :** Capture exceptions avec retour `0.0` par défaut
    - RETOUR : float - Stabilité entre 0.0 (instable) et 1.0 (stable)
    - UTILITÉ : Mesure fiabilité des prédictions via consistance de la confiance

13. get_confidence_percentile.txt (HybridBaccaratPredictor.get_confidence_percentile - Percentile confiance)
    - Lignes 10518-10548 dans hbp.py (31 lignes)
    - FONCTION : Calcule percentile d'une confiance donnée par rapport à l'historique récent pour évaluation relative
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * confidence (float) - Valeur de confiance à évaluer
      * window_size (int, optionnel) - Taille fenêtre de référence (défaut: 50)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION CONFIANCE :** Vérifie `if not isinstance(confidence, (int, float)):` avec retour `50.0` si type incorrect
      * **VALIDATION HISTORIQUE :** Teste `if not self.prediction_history:` avec retour `50.0` si aucune donnée
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
      * **COLLECTE CONFIANCES :** Extrait `confidence_values = [pred.get('confidence', 0.5) for pred in self.prediction_history[-actual_window:]]`
      * **VALIDATION DONNÉES :** Vérifie `if not confidence_values:` avec retour `50.0` si liste vide
      * **CALCUL PERCENTILE :** Utilise `percentile = (np.sum(np.array(confidence_values) <= confidence) / len(confidence_values)) * 100` pour position relative
      * **GESTION ERREURS :** Capture exceptions avec retour `50.0` par défaut
    - RETOUR : float - Percentile entre 0.0 et 100.0
    - UTILITÉ : Évaluation relative de la confiance pour détection valeurs exceptionnelles

14. is_confidence_anomaly.txt (HybridBaccaratPredictor.is_confidence_anomaly - Détection anomalie confiance)
    - Lignes 10550-10590 dans hbp.py (41 lignes)
    - FONCTION : Détecte anomalies dans confiance via analyse statistique avec seuils adaptatifs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * confidence (float) - Valeur de confiance à tester
      * sensitivity (float, optionnel) - Sensibilité détection (défaut: 2.0)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION CONFIANCE :** Vérifie `if not isinstance(confidence, (int, float)):` avec retour `True` si type incorrect
      * **VALIDATION HISTORIQUE :** Teste `if not self.prediction_history or len(self.prediction_history) < 10:` avec retour `False` si données insuffisantes
      * **COLLECTE HISTORIQUE :** Extrait `confidence_values = [pred.get('confidence', 0.5) for pred in self.prediction_history[-50:]]` sur 50 dernières valeurs
      * **CALCUL STATISTIQUES :** Utilise `mean_confidence = np.mean(confidence_values)` et `std_confidence = np.std(confidence_values)`
      * **SEUILS ANOMALIE :** Calcule `lower_bound = mean_confidence - sensitivity * std_confidence` et `upper_bound = mean_confidence + sensitivity * std_confidence`
      * **DÉTECTION ANOMALIE :** Teste `is_anomaly = confidence < lower_bound or confidence > upper_bound` pour valeurs aberrantes
      * **LOGGING ANOMALIE :** Si anomalie détectée, enregistre `logger.warning(f"Anomalie confiance détectée: {confidence:.3f} (bornes: [{lower_bound:.3f}, {upper_bound:.3f}])")` pour alerte
      * **GESTION ERREURS :** Capture exceptions avec retour `False` par défaut
    - RETOUR : bool - True si anomalie détectée, False sinon
    - UTILITÉ : Détection précoce de dysfonctionnements ou situations exceptionnelles

15. get_confidence_distribution.txt (HybridBaccaratPredictor.get_confidence_distribution - Distribution confiance)
    - Lignes 10592-10632 dans hbp.py (41 lignes)
    - FONCTION : Analyse distribution de confiance sur historique avec statistiques descriptives complètes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 100)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour dictionnaire vide si aucune donnée
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
      * **COLLECTE CONFIANCES :** Extrait `confidence_values = [pred.get('confidence', 0.5) for pred in self.prediction_history[-actual_window:]]`
      * **VALIDATION DONNÉES :** Teste `if not confidence_values:` avec retour dictionnaire vide si liste vide
      * **STATISTIQUES DESCRIPTIVES :** Calcule :
        - `mean = np.mean(confidence_values)` pour moyenne
        - `median = np.median(confidence_values)` pour médiane
        - `std = np.std(confidence_values)` pour écart-type
        - `min_val = np.min(confidence_values)` et `max_val = np.max(confidence_values)` pour bornes
      * **QUARTILES :** Utilise `q25 = np.percentile(confidence_values, 25)` et `q75 = np.percentile(confidence_values, 75)` pour quartiles
      * **CONSTRUCTION RÉSULTAT :** Retourne dictionnaire complet avec toutes statistiques
      * **GESTION ERREURS :** Capture exceptions avec retour dictionnaire vide
    - RETOUR : Dict[str, float] - Statistiques distribution (mean, median, std, min, max, q25, q75)
    - UTILITÉ : Analyse complète distribution pour monitoring et diagnostic système

16. calculate_confidence_momentum.txt (HybridBaccaratPredictor.calculate_confidence_momentum - Momentum confiance)
    - Lignes 10634-10674 dans hbp.py (41 lignes)
    - FONCTION : Calcule momentum de confiance via moyenne mobile pondérée avec poids décroissants
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 10)
      * decay_factor (float, optionnel) - Facteur de décroissance des poids (défaut: 0.9)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour `0.5` si aucune donnée
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
      * **COLLECTE CONFIANCES :** Extrait `confidence_values = [pred.get('confidence', 0.5) for pred in self.prediction_history[-actual_window:]]`
      * **VALIDATION DONNÉES :** Teste `if not confidence_values:` avec retour `0.5` si liste vide
      * **CALCUL POIDS :** Génère `weights = [decay_factor ** i for i in range(len(confidence_values))]` avec poids décroissants
      * **INVERSION POIDS :** Applique `weights.reverse()` pour donner plus de poids aux valeurs récentes
      * **MOYENNE PONDÉRÉE :** Calcule `weighted_sum = sum(conf * weight for conf, weight in zip(confidence_values, weights))` et `total_weight = sum(weights)`
      * **MOMENTUM FINAL :** Utilise `momentum = weighted_sum / total_weight` pour moyenne pondérée
      * **GESTION ERREURS :** Capture exceptions avec retour `0.5` par défaut
    - RETOUR : float - Momentum de confiance entre 0.0 et 1.0
    - UTILITÉ : Tendance pondérée récente pour prédiction évolution confiance

17. get_confidence_volatility.txt (HybridBaccaratPredictor.get_confidence_volatility - Volatilité confiance)
    - Lignes 10676-10706 dans hbp.py (31 lignes)
    - FONCTION : Calcule volatilité de confiance via écart-type des variations successives
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 20)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history or len(self.prediction_history) < 2:` avec retour `0.0` si données insuffisantes
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
      * **COLLECTE CONFIANCES :** Extrait `confidence_values = [pred.get('confidence', 0.5) for pred in self.prediction_history[-actual_window:]]`
      * **CALCUL VARIATIONS :** Utilise `differences = [confidence_values[i] - confidence_values[i-1] for i in range(1, len(confidence_values))]` pour variations successives
      * **VALIDATION VARIATIONS :** Teste `if not differences:` avec retour `0.0` si aucune variation
      * **VOLATILITÉ :** Calcule `volatility = np.std(differences)` pour écart-type des variations
      * **GESTION ERREURS :** Capture exceptions avec retour `0.0` par défaut
    - RETOUR : float - Volatilité (écart-type des variations)
    - UTILITÉ : Mesure instabilité confiance pour détection périodes turbulentes

18. is_confidence_stable.txt (HybridBaccaratPredictor.is_confidence_stable - Test stabilité confiance)
    - Lignes 10708-10738 dans hbp.py (31 lignes)
    - FONCTION : Détermine si confiance est stable via seuil de volatilité avec paramètres configurables
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * volatility_threshold (float, optionnel) - Seuil de volatilité (défaut: 0.1)
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 15)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL VOLATILITÉ :** Appelle `current_volatility = self.get_confidence_volatility(window_size)` pour mesure actuelle
      * **COMPARAISON SEUIL :** Teste `is_stable = current_volatility <= volatility_threshold` pour détermination stabilité
      * **LOGGING DEBUG :** Enregistre `logger.debug(f"Test stabilité confiance: volatilité={current_volatility:.3f}, seuil={volatility_threshold:.3f}, stable={is_stable}")` pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec retour `False` par défaut
    - RETOUR : bool - True si confiance stable, False sinon
    - UTILITÉ : Décision binaire pour adaptation stratégie selon stabilité confiance

19. get_confidence_range.txt (HybridBaccaratPredictor.get_confidence_range - Plage confiance)
    - Lignes 10740-10770 dans hbp.py (31 lignes)
    - FONCTION : Calcule plage de confiance (max - min) sur fenêtre récente pour mesure dispersion
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 20)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour `0.0` si aucune donnée
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
      * **COLLECTE CONFIANCES :** Extrait `confidence_values = [pred.get('confidence', 0.5) for pred in self.prediction_history[-actual_window:]]`
      * **VALIDATION DONNÉES :** Teste `if not confidence_values:` avec retour `0.0` si liste vide
      * **CALCUL PLAGE :** Utilise `confidence_range = max(confidence_values) - min(confidence_values)` pour dispersion
      * **GESTION ERREURS :** Capture exceptions avec retour `0.0` par défaut
    - RETOUR : float - Plage de confiance (0.0 à 1.0)
    - UTILITÉ : Mesure simple de la variabilité confiance sur période récente

20. normalize_confidence_score.txt (HybridBaccaratPredictor.normalize_confidence_score - Normalisation score confiance)
    - Lignes 10772-10802 dans hbp.py (31 lignes)
    - FONCTION : Normalise score de confiance selon distribution historique avec transformation percentile
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * raw_confidence (float) - Score de confiance brut à normaliser
      * reference_window (int, optionnel) - Fenêtre de référence (défaut: 100)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION CONFIANCE :** Vérifie `if not isinstance(raw_confidence, (int, float)):` avec retour `0.5` si type incorrect
      * **CALCUL PERCENTILE :** Appelle `percentile = self.get_confidence_percentile(raw_confidence, reference_window)` pour position relative
      * **TRANSFORMATION SIGMOÏDE :** Utilise `normalized = 1 / (1 + np.exp(-0.1 * (percentile - 50)))` pour transformation douce
      * **BORNES FINALES :** Applique `normalized = max(0.0, min(1.0, normalized))` pour garantir [0,1]
      * **GESTION ERREURS :** Capture exceptions avec retour `0.5` par défaut
    - RETOUR : float - Confiance normalisée entre 0.0 et 1.0
    - UTILITÉ : Standardisation confiance selon contexte historique pour comparaisons équitables

================================================================================
SECTION 2 : EVALUATION METRIQUES
================================================================================

1. _calculate_training_metrics.txt (HybridBaccaratPredictor._calculate_training_metrics - Calcul métriques entraînement)
   - Lignes 2582-2720 dans hbp.py (139 lignes)
   - FONCTION : Calcule métriques complètes d'entraînement avec validation croisée et évaluation performance sur données test
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_lgbm (numpy.ndarray) - Features LGBM pour évaluation
     * y_lgbm (numpy.ndarray) - Labels pour évaluation LGBM
     * X_lstm (numpy.ndarray) - Features LSTM pour évaluation
     * y_lstm (numpy.ndarray) - Labels pour évaluation LSTM
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` et `metrics = {}` pour collecte résultats
     * **VALIDATION DONNÉES :** Vérifie `if X_lgbm is None or y_lgbm is None:` avec logging warning et retour dictionnaire vide si données manquantes
     * **SPLIT VALIDATION :** Utilise `test_size = min(0.3, max(0.1, len(y_lgbm) * 0.2 / len(y_lgbm)))` pour adaptation taille test selon données disponibles
     * **DIVISION DONNÉES :** Applique `X_train, X_test, y_train, y_test = train_test_split(X_lgbm, y_lgbm, test_size=test_size, random_state=42, stratify=y_lgbm)` pour split stratifié
     * **ÉVALUATION LGBM :** Si modèle entraîné, calcule :
       - `y_pred_lgbm = self.calibrated_lgbm.predict(X_test)` pour prédictions
       - `accuracy_lgbm = accuracy_score(y_test, y_pred_lgbm)` pour précision
       - `precision_lgbm = precision_score(y_test, y_pred_lgbm, average='weighted', zero_division=0)` pour précision pondérée
       - `recall_lgbm = recall_score(y_test, y_pred_lgbm, average='weighted', zero_division=0)` pour rappel pondéré
       - `f1_lgbm = f1_score(y_test, y_pred_lgbm, average='weighted', zero_division=0)` pour F1-score pondéré
     * **ÉVALUATION LSTM :** Si modèle LSTM disponible et données LSTM fournies :
       - Prépare données avec reshape approprié pour TensorFlow
       - Calcule prédictions avec `y_pred_lstm_proba = self.lstm.predict(X_lstm_test, verbose=0)`
       - Convertit probabilités en classes avec `y_pred_lstm = np.argmax(y_pred_lstm_proba, axis=1)`
       - Calcule métriques similaires à LGBM
     * **MÉTRIQUES HYBRIDES :** Évalue performance système complet :
       - Génère prédictions hybrides sur ensemble test
       - Calcule métriques globales du système
       - Compare performance individuelle vs hybride
     * **VALIDATION CROISÉE :** Si données suffisantes, effectue validation croisée 5-fold pour robustesse
     * **LOGGING RÉSULTATS :** Enregistre `logger_instance.info(f"Métriques calculées - LGBM: {accuracy_lgbm:.3f}, LSTM: {accuracy_lstm:.3f}")` pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour métriques partielles
   - RETOUR : Dict[str, float] - Dictionnaire complet des métriques (accuracy, precision, recall, f1 par modèle)
   - UTILITÉ : Évaluation complète performance avec métriques standardisées pour monitoring et optimisation

2. _update_method_performance.txt (HybridBaccaratPredictor._update_method_performance - MAJ performance méthodes)
   - Lignes 2092-2170 dans hbp.py (79 lignes)
   - FONCTION : Met à jour statistiques de performance pour chaque méthode avec fenêtre glissante et calcul précision
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * method_predictions (Dict[str, str]) - Prédictions par méthode ('player'/'banker')
     * actual_outcome (str) - Résultat réel observé
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PARAMÈTRES :** Vérifie `if not method_predictions or not actual_outcome:` avec logging warning et retour si paramètres invalides
     * **NORMALISATION OUTCOME :** Applique `actual_outcome = actual_outcome.lower().strip()` pour standardisation
     * **VALIDATION OUTCOME :** Teste `if actual_outcome not in ['player', 'banker']:` avec logging warning et retour si valeur invalide
     * **PROTECTION CONCURRENCE :** Utilise `with self.weights_lock:` pour accès thread-safe aux statistiques
     * **MISE À JOUR PAR MÉTHODE :** Itère `for method, prediction in method_predictions.items():` :
       - Normalise `prediction = prediction.lower().strip()` pour cohérence
       - Valide `if prediction not in ['player', 'banker']:` avec skip si invalide
       - Incrémente `self.method_performance[method]['total'] += 1` pour compteur total
       - Si `prediction == actual_outcome:`, incrémente `self.method_performance[method]['correct'] += 1` pour succès
     * **CALCUL PRÉCISION :** Pour chaque méthode, calcule `accuracy = correct / total` si `total > 0`
     * **HISTORIQUE GLISSANT :** Ajoute précision à `self.method_performance[method]['accuracy_history'].append(accuracy)`
     * **LIMITATION HISTORIQUE :** Maintient `if len(accuracy_history) > 100:` avec `accuracy_history.pop(0)` pour fenêtre glissante
     * **LOGGING DEBUG :** Enregistre `logger.debug(f"Performance mise à jour - {method}: {accuracy:.3f} ({correct}/{total})")` pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé pour debugging
   - RETOUR : None - Met à jour directement les statistiques internes
   - UTILITÉ : Suivi continu performance individuelle des méthodes pour ajustement poids adaptatifs

3. _calculate_method_weights.txt (HybridBaccaratPredictor._calculate_method_weights - Calcul poids méthodes)
   - Lignes 2722-2820 dans hbp.py (99 lignes)
   - FONCTION : Calcule poids adaptatifs des méthodes basés sur performance récente avec lissage et normalisation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION CONCURRENCE :** Utilise `with self.weights_lock:` pour accès thread-safe aux poids et statistiques
     * **INITIALISATION :** Crée `new_weights = {}` et récupère `smoothing_factor = getattr(self.config, 'weight_smoothing_factor', 0.1)` pour lissage
     * **CALCUL PAR MÉTHODE :** Itère `for method in self.method_performance.keys():` :
       - Récupère `correct = self.method_performance[method]['correct']` et `total = self.method_performance[method]['total']`
       - Calcule `current_accuracy = correct / total` si `total > 0`, sinon utilise `base_accuracy = self.config.initial_weights.get(method, 0.33)`
       - Applique bonus performance avec `if current_accuracy > 0.6: performance_bonus = (current_accuracy - 0.6) * 0.5`
       - Calcule poids brut avec `raw_weight = base_accuracy + performance_bonus`
     * **ANALYSE TENDANCE :** Si historique suffisant, calcule tendance récente :
       - Utilise `recent_history = accuracy_history[-10:]` pour 10 dernières valeurs
       - Calcule pente avec régression linéaire `slope = np.polyfit(range(len(recent_history)), recent_history, 1)[0]`
       - Applique ajustement tendance avec `trend_adjustment = slope * 0.2`
     * **STABILITÉ :** Évalue stabilité via coefficient de variation :
       - Calcule `cv = np.std(recent_history) / np.mean(recent_history)` si moyenne > 0
       - Applique pénalité instabilité avec `stability_penalty = cv * 0.1`
     * **POIDS FINAL :** Combine tous facteurs : `final_weight = raw_weight + trend_adjustment - stability_penalty`
     * **NORMALISATION :** Assure `total_weight = sum(new_weights.values())` puis normalise avec `new_weights[method] = weight / total_weight`
     * **LISSAGE :** Applique `self.weights[method] = (1 - smoothing_factor) * self.weights[method] + smoothing_factor * new_weights[method]` pour transition douce
     * **MISE À JOUR BEST :** Si amélioration globale, met à jour `self.best_weights = self.weights.copy()`
     * **LOGGING :** Enregistre nouveaux poids pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec conservation poids actuels
   - RETOUR : None - Met à jour directement les poids internes
   - UTILITÉ : Adaptation automatique des poids selon performance avec lissage pour stabilité

4. get_method_accuracy.txt (HybridBaccaratPredictor.get_method_accuracy - Précision méthode)
   - Lignes 8416-8446 dans hbp.py (31 lignes)
   - FONCTION : Récupère précision actuelle d'une méthode spécifique avec gestion des cas d'erreur
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * method (str) - Nom de la méthode ('markov', 'lgbm', 'lstm')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MÉTHODE :** Vérifie `if not method or not isinstance(method, str):` avec retour `0.5` si paramètre invalide
     * **NORMALISATION :** Applique `method = method.lower().strip()` pour standardisation
     * **PROTECTION CONCURRENCE :** Utilise `with self.weights_lock:` pour accès thread-safe aux statistiques
     * **VÉRIFICATION EXISTENCE :** Teste `if method not in self.method_performance:` avec retour `0.5` si méthode inconnue
     * **EXTRACTION DONNÉES :** Récupère `correct = self.method_performance[method]['correct']` et `total = self.method_performance[method]['total']`
     * **CALCUL PRÉCISION :** Utilise `accuracy = correct / total` si `total > 0`, sinon retourne `0.5` (performance neutre)
     * **GESTION ERREURS :** Capture exceptions avec retour `0.5` par défaut
   - RETOUR : float - Précision entre 0.0 et 1.0
   - UTILITÉ : Accès rapide à la performance individuelle des méthodes pour monitoring et décisions

5. get_overall_accuracy.txt (HybridBaccaratPredictor.get_overall_accuracy - Précision globale)
   - Lignes 8448-8478 dans hbp.py (31 lignes)
   - FONCTION : Calcule précision globale du système hybride sur historique complet des prédictions
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour `0.5` si aucune prédiction
     * **COMPTAGE SUCCÈS :** Initialise `correct_predictions = 0` et `total_predictions = len(self.prediction_history)`
     * **PARCOURS HISTORIQUE :** Itère `for prediction in self.prediction_history:` :
       - Extrait `predicted_outcome = prediction.get('prediction')`
       - Extrait `actual_outcome = prediction.get('actual_outcome')`
       - Valide présence des deux valeurs
       - Incrémente `correct_predictions` si `predicted_outcome == actual_outcome`
     * **CALCUL PRÉCISION :** Utilise `overall_accuracy = correct_predictions / total_predictions` si `total_predictions > 0`
     * **GESTION ERREURS :** Capture exceptions avec retour `0.5` par défaut
   - RETOUR : float - Précision globale entre 0.0 et 1.0
   - UTILITÉ : Mesure performance générale du système pour évaluation globale et comparaisons

6. _draw_confusion_matrix.txt (HybridBaccaratPredictor._draw_confusion_matrix - Dessin matrice confusion)
   - Lignes 8107-8296 dans hbp.py (190 lignes)
   - FONCTION : Dessine matrice de confusion interactive sur canvas Tkinter avec couleurs et annotations détaillées
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * canvas (tk.Canvas) - Canvas Tkinter pour dessin
     * x (int) - Position X de départ
     * y (int) - Position Y de départ
     * size (int) - Taille de la matrice en pixels
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL MATRICE :** Initialise `confusion_matrix = {'PP': 0, 'PB': 0, 'BP': 0, 'BB': 0}` pour compteurs
     * **PARCOURS HISTORIQUE :** Itère `for prediction in self.prediction_history:` :
       - Extrait `predicted = prediction.get('prediction', '').lower()`
       - Extrait `actual = prediction.get('actual_outcome', '').lower()`
       - Valide et normalise valeurs ('player' -> 'P', 'banker' -> 'B')
       - Incrémente compteur approprié `confusion_matrix[f'{pred_key}{actual_key}'] += 1`
     * **CALCUL TOTAL :** Détermine `total_predictions = sum(confusion_matrix.values())` pour normalisation
     * **DESSIN STRUCTURE :** Crée grille 2x2 avec :
       - `cell_size = size // 2` pour taille cellules
       - Bordures et séparateurs avec `canvas.create_rectangle()`
       - Labels axes avec `canvas.create_text()` pour "Prédiction" et "Réel"
     * **DESSIN CELLULES :** Pour chaque cellule :
       - Calcule intensité avec `intensity = count / max(1, total_predictions)` pour normalisation
       - Détermine couleur avec `color = self._get_color_for_intensity(intensity)` (gradient bleu)
       - Dessine rectangle coloré avec `canvas.create_rectangle(x1, y1, x2, y2, fill=color)`
       - Ajoute texte avec count et pourcentage `f"{count}\n({percentage:.1f}%)"`
     * **ANNOTATIONS :** Ajoute labels descriptifs :
       - "Player/Player", "Player/Banker", "Banker/Player", "Banker/Banker"
       - Positionnement centré dans chaque cellule
     * **MÉTRIQUES DÉRIVÉES :** Calcule et affiche :
       - Précision par classe : `precision_player = PP / (PP + BP)` si dénominateur > 0
       - Rappel par classe : `recall_player = PP / (PP + PB)` si dénominateur > 0
       - F1-score par classe avec formule harmonique
     * **GESTION ERREURS :** Capture exceptions avec affichage message d'erreur sur canvas
   - RETOUR : None - Dessine directement sur le canvas
   - UTILITÉ : Visualisation interactive performance avec détails par classe et métriques dérivées

7. calculate_prediction_metrics.txt (HybridBaccaratPredictor.calculate_prediction_metrics - Calcul métriques prédiction)
   - Lignes 8480-8550 dans hbp.py (71 lignes)
   - FONCTION : Calcule métriques détaillées de prédiction avec analyse par classe et métriques globales
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 50)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour dictionnaire vide si aucune donnée
     * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
     * **COLLECTE DONNÉES :** Extrait `recent_predictions = self.prediction_history[-actual_window:]` pour analyse récente
     * **INITIALISATION COMPTEURS :** Crée dictionnaires pour confusion matrix : `true_positives = {'player': 0, 'banker': 0}`, etc.
     * **PARCOURS PRÉDICTIONS :** Itère `for pred in recent_predictions:` :
       - Extrait et normalise `predicted = pred.get('prediction', '').lower()`
       - Extrait et normalise `actual = pred.get('actual_outcome', '').lower()`
       - Met à jour compteurs appropriés selon combinaison prédiction/réalité
     * **CALCUL MÉTRIQUES PAR CLASSE :** Pour chaque classe ('player', 'banker') :
       - `precision = tp / (tp + fp)` si dénominateur > 0, sinon 0.0
       - `recall = tp / (tp + fn)` si dénominateur > 0, sinon 0.0
       - `f1_score = 2 * (precision * recall) / (precision + recall)` si dénominateur > 0, sinon 0.0
     * **MÉTRIQUES GLOBALES :** Calcule :
       - `accuracy = (tp_player + tp_banker) / total_predictions` pour précision globale
       - `macro_precision = (precision_player + precision_banker) / 2` pour précision macro
       - `macro_recall = (recall_player + recall_banker) / 2` pour rappel macro
       - `macro_f1 = (f1_player + f1_banker) / 2` pour F1 macro
     * **CONSTRUCTION RÉSULTAT :** Retourne dictionnaire structuré avec toutes métriques
     * **GESTION ERREURS :** Capture exceptions avec retour dictionnaire vide
   - RETOUR : Dict[str, Any] - Métriques complètes (accuracy, precision, recall, f1 par classe et globales)
   - UTILITÉ : Évaluation détaillée performance avec métriques standardisées pour monitoring et optimisation

8. get_performance_summary.txt (HybridBaccaratPredictor.get_performance_summary - Résumé performance)
   - Lignes 8552-8612 dans hbp.py (61 lignes)
   - FONCTION : Génère résumé complet de performance avec statistiques globales et par méthode
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Crée `summary = {}` pour collecte données et `total_predictions = len(self.prediction_history)`
     * **MÉTRIQUES GLOBALES :** Calcule :
       - `overall_accuracy = self.get_overall_accuracy()` pour précision générale
       - `recent_accuracy = self.get_recent_accuracy(window_size=20)` pour performance récente
       - `prediction_metrics = self.calculate_prediction_metrics(window_size=50)` pour métriques détaillées
     * **PERFORMANCE PAR MÉTHODE :** Itère `for method in ['markov', 'lgbm', 'lstm']:` :
       - Récupère `method_accuracy = self.get_method_accuracy(method)` pour précision individuelle
       - Calcule `method_weight = self.weights.get(method, 0.0)` pour poids actuel
       - Ajoute à résumé avec clés structurées
     * **STATISTIQUES HISTORIQUE :** Analyse historique prédictions :
       - Compte prédictions par type de recommandation (WAIT/NON-WAIT)
       - Calcule taux de succès par type
       - Détermine tendances récentes
     * **MÉTRIQUES CONFIANCE :** Intègre :
       - Distribution confiance via `self.get_confidence_distribution()`
       - Stabilité confiance via `self.calculate_confidence_stability()`
       - Volatilité confiance via `self.get_confidence_volatility()`
     * **CONSTRUCTION RÉSUMÉ :** Structure résultat avec sections :
       - 'global' : métriques générales
       - 'methods' : performance par méthode
       - 'confidence' : statistiques confiance
       - 'trends' : tendances et évolutions
     * **GESTION ERREURS :** Capture exceptions avec résumé partiel
   - RETOUR : Dict[str, Any] - Résumé structuré complet de performance
   - UTILITÉ : Vue d'ensemble performance pour monitoring et reporting

9. analyze_prediction_patterns.txt (HybridBaccaratPredictor.analyze_prediction_patterns - Analyse patterns prédiction)
   - Lignes 8614-8684 dans hbp.py (71 lignes)
   - FONCTION : Analyse patterns dans historique prédictions pour identifier tendances et anomalies
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 100)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history or len(self.prediction_history) < 10:` avec retour dictionnaire vide
     * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
     * **COLLECTE SÉQUENCES :** Extrait séquences de prédictions et résultats pour analyse patterns
     * **ANALYSE STREAKS :** Identifie séquences consécutives :
       - Compte streaks de succès/échecs
       - Détermine longueur moyenne et maximale
       - Calcule distribution des longueurs
     * **PATTERNS TEMPORELS :** Analyse évolution dans le temps :
       - Divise fenêtre en segments temporels
       - Compare performance entre segments
       - Identifie tendances d'amélioration/dégradation
     * **CORRÉLATIONS :** Examine corrélations entre :
       - Confiance et succès de prédiction
       - Type de recommandation et performance
       - Méthode dominante et résultats
     * **DÉTECTION ANOMALIES :** Identifie :
       - Périodes de performance exceptionnelle (positive/négative)
       - Changements brusques de patterns
       - Incohérences dans comportement
     * **MÉTRIQUES PATTERNS :** Calcule :
       - Entropie des séquences pour mesurer prévisibilité
       - Autocorrélation pour détecter cycles
       - Variance glissante pour stabilité
     * **CONSTRUCTION RÉSULTAT :** Structure analyse avec sections détaillées
     * **GESTION ERREURS :** Capture exceptions avec analyse partielle
   - RETOUR : Dict[str, Any] - Analyse complète des patterns identifiés
   - UTILITÉ : Compréhension comportement système pour optimisation et diagnostic

10. calculate_method_correlation.txt (HybridBaccaratPredictor.calculate_method_correlation - Corrélation méthodes)
    - Lignes 8686-8746 dans hbp.py (61 lignes)
    - FONCTION : Calcule corrélations entre prédictions des différentes méthodes pour analyse complémentarité
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 50)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history or len(self.prediction_history) < 10:` avec retour matrice vide
      * **EXTRACTION DONNÉES :** Collecte prédictions par méthode sur fenêtre récente
      * **CONVERSION NUMÉRIQUE :** Transforme prédictions texte en valeurs numériques :
        - 'player' -> 0, 'banker' -> 1 pour calculs statistiques
        - Gère valeurs manquantes avec imputation appropriée
      * **MATRICE CORRÉLATION :** Calcule corrélations Pearson entre toutes paires de méthodes :
        - Markov vs LGBM, Markov vs LSTM, LGBM vs LSTM
        - Utilise `np.corrcoef()` pour calculs robustes
        - Gère cas de variance nulle avec corrélation 0.0
      * **ANALYSE COMPLÉMENTARITÉ :** Évalue :
        - Corrélations faibles = méthodes complémentaires (bon pour ensemble)
        - Corrélations fortes = méthodes redondantes
        - Corrélations négatives = méthodes opposées
      * **MÉTRIQUES DÉRIVÉES :** Calcule :
        - Diversité moyenne = 1 - moyenne(|corrélations|)
        - Redondance maximale = max(|corrélations|)
        - Score complémentarité global
      * **INTERPRÉTATION :** Ajoute interprétations textuelles :
        - "Très complémentaires" si corrélation < 0.3
        - "Modérément corrélées" si 0.3 ≤ corrélation < 0.7
        - "Fortement corrélées" si corrélation ≥ 0.7
      * **GESTION ERREURS :** Capture exceptions avec matrice identité par défaut
    - RETOUR : Dict[str, Any] - Matrice corrélation et métriques dérivées
    - UTILITÉ : Optimisation composition ensemble et détection redondances

11. evaluate_prediction_stability.txt (HybridBaccaratPredictor.evaluate_prediction_stability - Évaluation stabilité prédiction)
    - Lignes 8748-8808 dans hbp.py (61 lignes)
    - FONCTION : Évalue stabilité des prédictions via analyse variance et consistance temporelle
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * window_size (int, optionnel) - Taille fenêtre d'analyse (défaut: 30)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history or len(self.prediction_history) < 5:` avec retour dictionnaire vide
      * **EXTRACTION FENÊTRE :** Utilise `actual_window = min(window_size, len(self.prediction_history))` pour adaptation
      * **COLLECTE MÉTRIQUES :** Extrait séries temporelles :
        - Confiance des prédictions
        - Probabilités Player/Banker
        - Incertitudes épistémique/aléatoire
      * **ANALYSE VARIANCE :** Calcule stabilité via :
        - `confidence_stability = 1.0 - np.std(confidence_values) / max(0.1, np.mean(confidence_values))` pour coefficient variation inversé
        - `probability_variance = np.var([abs(p['player'] - 0.5) for p in predictions])` pour mesure dispersion probabilités
        - `uncertainty_trend = np.polyfit(range(len(uncertainties)), uncertainties, 1)[0]` pour tendance incertitude
      * **CONSISTANCE TEMPORELLE :** Analyse :
        - Changements brusques avec `sudden_changes = sum(1 for i in range(1, len(values)) if abs(values[i] - values[i-1]) > threshold)`
        - Oscillations via détection pics et creux
        - Dérive temporelle avec régression linéaire
      * **MÉTRIQUES STABILITÉ :** Calcule scores composites :
        - Score stabilité confiance (0-1, 1=très stable)
        - Score consistance prédictions (0-1, 1=très consistant)
        - Score stabilité globale (moyenne pondérée)
      * **CLASSIFICATION :** Détermine niveau stabilité :
        - "Très stable" si score > 0.8
        - "Stable" si 0.6 ≤ score ≤ 0.8
        - "Modérément stable" si 0.4 ≤ score < 0.6
        - "Instable" si score < 0.4
      * **RECOMMANDATIONS :** Génère suggestions basées sur analyse :
        - Ajustement paramètres si instabilité détectée
        - Augmentation fenêtre lissage si oscillations
        - Recalibration si dérive temporelle
      * **GESTION ERREURS :** Capture exceptions avec évaluation neutre
    - RETOUR : Dict[str, Any] - Évaluation complète stabilité avec recommandations
    - UTILITÉ : Diagnostic stabilité système pour optimisation et maintenance

12. generate_performance_report.txt (HybridBaccaratPredictor.generate_performance_report - Génération rapport performance)
    - Lignes 8810-8900 dans hbp.py (91 lignes)
    - FONCTION : Génère rapport détaillé de performance avec analyses multiples et recommandations
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * include_charts (bool, optionnel) - Inclure données pour graphiques (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **COLLECTE DONNÉES :** Rassemble toutes métriques disponibles :
        - Résumé performance via `self.get_performance_summary()`
        - Analyse patterns via `self.analyze_prediction_patterns()`
        - Corrélations méthodes via `self.calculate_method_correlation()`
        - Stabilité via `self.evaluate_prediction_stability()`
      * **MÉTRIQUES TEMPORELLES :** Analyse évolution dans le temps :
        - Performance par période (dernière heure, jour, semaine)
        - Tendances à court et long terme
        - Identification cycles et saisonnalité
      * **ANALYSE COMPARATIVE :** Compare avec références :
        - Performance vs objectifs configurés
        - Évolution vs période précédente
        - Benchmarking vs performance théorique
      * **DÉTECTION PROBLÈMES :** Identifie issues potentielles :
        - Dégradation performance récente
        - Déséquilibres entre méthodes
        - Anomalies dans patterns
        - Instabilités système
      * **RECOMMANDATIONS :** Génère suggestions actionables :
        - Ajustements paramètres spécifiques
        - Optimisations configuration
        - Actions maintenance préventive
        - Stratégies amélioration
      * **FORMATAGE RAPPORT :** Structure résultat avec sections :
        - Executive Summary avec KPIs principaux
        - Analyse détaillée par composant
        - Graphiques et visualisations (si demandé)
        - Plan d'action recommandé
      * **EXPORT DONNÉES :** Si `include_charts=True`, prépare données pour visualisation :
        - Séries temporelles pour graphiques
        - Matrices pour heatmaps
        - Distributions pour histogrammes
      * **GESTION ERREURS :** Capture exceptions avec rapport partiel et alertes
    - RETOUR : Dict[str, Any] - Rapport complet structuré avec analyses et recommandations
    - UTILITÉ : Reporting complet pour monitoring, diagnostic et optimisation système

13. calculate_roi_metrics.txt (HybridBaccaratPredictor.calculate_roi_metrics - Calcul métriques ROI)
    - Lignes 8902-8962 dans hbp.py (61 lignes)
    - FONCTION : Calcule métriques de retour sur investissement basées sur performance prédictions
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * bet_amount (float, optionnel) - Montant mise de référence (défaut: 1.0)
      * payout_ratio (float, optionnel) - Ratio de paiement (défaut: 1.95)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PARAMÈTRES :** Vérifie `if bet_amount <= 0 or payout_ratio <= 1:` avec valeurs par défaut si invalides
      * **SIMULATION PARIS :** Parcourt historique prédictions :
        - Pour chaque prédiction avec recommandation NON-WAIT
        - Simule mise de `bet_amount` sur prédiction
        - Calcule gain/perte selon résultat réel
      * **CALCUL GAINS :** Détermine résultats financiers :
        - `total_bets = nombre_predictions_non_wait * bet_amount` pour investissement total
        - `total_winnings = nombre_predictions_correctes * bet_amount * payout_ratio` pour gains bruts
        - `net_profit = total_winnings - total_bets` pour profit net
      * **MÉTRIQUES ROI :** Calcule indicateurs financiers :
        - `roi_percentage = (net_profit / total_bets) * 100` pour ROI en pourcentage
        - `win_rate = nombre_predictions_correctes / nombre_predictions_non_wait` pour taux de réussite
        - `average_profit_per_bet = net_profit / nombre_predictions_non_wait` pour profit moyen par mise
      * **ANALYSE RISQUE :** Évalue :
        - Variance des gains pour mesure risque
        - Drawdown maximum (perte maximale consécutive)
        - Ratio Sharpe adapté pour jeux
      * **PROJECTIONS :** Calcule estimations futures :
        - ROI projeté basé sur tendance récente
        - Seuil de rentabilité (break-even point)
        - Temps estimé pour objectifs profit
      * **MÉTRIQUES AVANCÉES :** Inclut :
        - Kelly Criterion pour taille mise optimale
        - Value at Risk (VaR) pour gestion risque
        - Coefficient de variation des gains
      * **GESTION ERREURS :** Capture exceptions avec métriques neutres
    - RETOUR : Dict[str, float] - Métriques ROI complètes avec projections
    - UTILITÉ : Évaluation financière performance pour décisions investissement

14. benchmark_against_baseline.txt (HybridBaccaratPredictor.benchmark_against_baseline - Benchmark vs baseline)
    - Lignes 8964-9024 dans hbp.py (61 lignes)
    - FONCTION : Compare performance système contre stratégies baseline pour validation efficacité
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * baseline_strategy (str, optionnel) - Stratégie de référence (défaut: 'random')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **STRATÉGIES BASELINE :** Implémente références :
        - 'random' : Prédictions aléatoires 50/50
        - 'always_player' : Toujours prédire Player
        - 'always_banker' : Toujours prédire Banker
        - 'pattern_following' : Suit dernier résultat
        - 'pattern_opposite' : Oppose dernier résultat
      * **SIMULATION BASELINE :** Pour stratégie choisie :
        - Applique logique sur même historique
        - Calcule prédictions baseline pour chaque manche
        - Évalue performance selon mêmes métriques
      * **COMPARAISON MÉTRIQUES :** Compare système vs baseline :
        - Précision : `system_accuracy - baseline_accuracy`
        - ROI : différence de rentabilité
        - Stabilité : comparaison variance
        - Consistance : analyse écart-types
      * **TESTS STATISTIQUES :** Applique tests significativité :
        - Test t de Student pour différence moyennes
        - Test Chi-carré pour distribution prédictions
        - Test Kolmogorov-Smirnov pour distributions
      * **MÉTRIQUES AMÉLIORATION :** Calcule gains relatifs :
        - `improvement_percentage = ((system_metric - baseline_metric) / baseline_metric) * 100`
        - Lift factor = system_metric / baseline_metric
        - Significance level des améliorations
      * **ANALYSE CONTEXTE :** Évalue dans quelles conditions :
        - Système surperforme baseline
        - Baseline parfois meilleure
        - Facteurs influençant performance relative
      * **VALIDATION ROBUSTESSE :** Teste sur différentes périodes :
        - Performance récente vs historique
        - Stabilité amélioration dans le temps
        - Sensibilité aux conditions marché
      * **GESTION ERREURS :** Capture exceptions avec comparaison partielle
    - RETOUR : Dict[str, Any] - Analyse comparative complète avec tests statistiques
    - UTILITÉ : Validation objective efficacité système vs alternatives simples

15. export_metrics_data.txt (HybridBaccaratPredictor.export_metrics_data - Export données métriques)
    - Lignes 9026-9086 dans hbp.py (61 lignes)
    - FONCTION : Exporte données métriques vers formats externes pour analyse et archivage
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * export_format (str, optionnel) - Format export ('json', 'csv', 'excel') (défaut: 'json')
      * file_path (str, optionnel) - Chemin fichier de sortie (défaut: auto-généré)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **COLLECTE DONNÉES :** Rassemble toutes métriques :
        - Historique prédictions complet
        - Statistiques performance par méthode
        - Métriques confiance et stabilité
        - Données corrélation et patterns
      * **PRÉPARATION EXPORT :** Structure données selon format :
        - JSON : Dictionnaire hiérarchique complet
        - CSV : Tables plates avec relations
        - Excel : Feuilles multiples par catégorie
      * **FORMATAGE JSON :** Si format JSON :
        - Structure avec sections logiques
        - Métadonnées export (timestamp, version)
        - Compression optionnelle pour gros volumes
      * **FORMATAGE CSV :** Si format CSV :
        - Table principale prédictions
        - Tables secondaires métriques
        - Headers descriptifs et types données
      * **FORMATAGE EXCEL :** Si format Excel :
        - Feuille "Summary" avec KPIs
        - Feuille "Predictions" avec historique
        - Feuille "Metrics" avec statistiques
        - Feuille "Charts" avec données graphiques
      * **GÉNÉRATION CHEMIN :** Si pas spécifié :
        - `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")`
        - `file_path = f"metrics_export_{timestamp}.{extension}"`
      * **ÉCRITURE FICHIER :** Selon format :
        - JSON : `json.dump()` avec indentation
        - CSV : `pandas.to_csv()` si disponible
        - Excel : `pandas.to_excel()` avec formatage
      * **VALIDATION EXPORT :** Vérifie :
        - Fichier créé avec succès
        - Taille cohérente avec données
        - Lisibilité du fichier généré
      * **GESTION ERREURS :** Capture exceptions avec fallback JSON simple
    - RETOUR : str - Chemin fichier exporté ou None si échec
    - UTILITÉ : Archivage et analyse externe des données de performance

16. calculate_feature_importance.txt (HybridBaccaratPredictor.calculate_feature_importance - Importance features)
    - Lignes 9088-9148 dans hbp.py (61 lignes)
    - FONCTION : Calcule importance des features pour compréhension facteurs prédictifs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * method (str, optionnel) - Méthode d'analyse ('lgbm', 'correlation', 'permutation') (défaut: 'lgbm')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MÉTHODE :** Vérifie `if method not in ['lgbm', 'correlation', 'permutation']:` avec fallback 'lgbm'
      * **IMPORTANCE LGBM :** Si méthode 'lgbm' et modèle disponible :
        - Extrait `feature_importance = self.lgbm_base.feature_importances_` du modèle entraîné
        - Normalise importances avec `normalized_importance = importance / sum(importance)`
        - Mappe indices vers noms features descriptifs
      * **ANALYSE CORRÉLATION :** Si méthode 'correlation' :
        - Calcule corrélations Pearson entre chaque feature et target
        - Utilise valeurs absolues pour importance
        - Normalise pour somme = 1.0
      * **IMPORTANCE PERMUTATION :** Si méthode 'permutation' :
        - Pour chaque feature, permute valeurs aléatoirement
        - Mesure dégradation performance sur validation set
        - Importance = perte performance due à permutation
      * **NOMS FEATURES :** Mappe indices vers descriptions :
        - Feature 0 : "Ratio Player récent"
        - Feature 1 : "Streak actuel"
        - Feature 2 : "Pattern 3-grams"
        - etc. selon configuration features
      * **TRI ET RANKING :** Ordonne features par importance décroissante :
        - Crée liste tuples (nom_feature, importance)
        - Trie par importance descendante
        - Calcule importance cumulative
      * **ANALYSE SEUILS :** Identifie features critiques :
        - Top 20% features (importance cumulative)
        - Features significatives (importance > seuil)
        - Features négligeables (importance < 1%)
      * **INTERPRÉTATION :** Ajoute insights :
        - Features les plus prédictives
        - Redondances potentielles
        - Suggestions optimisation
      * **GESTION ERREURS :** Capture exceptions avec importance uniforme
    - RETOUR : Dict[str, float] - Importance par feature triée par ordre décroissant
    - UTILITÉ : Compréhension facteurs prédictifs pour optimisation features et interprétabilité

17. validate_model_performance.txt (HybridBaccaratPredictor.validate_model_performance - Validation performance modèle)
    - Lignes 9150-9210 dans hbp.py (61 lignes)
    - FONCTION : Valide performance modèle via tests statistiques et validation croisée
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * validation_method (str, optionnel) - Méthode validation ('holdout', 'cv', 'bootstrap') (défaut: 'holdout')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PRÉPARATION DONNÉES :** Collecte données pour validation :
        - Features LGBM et LSTM récentes
        - Labels correspondants
        - Split temporel pour éviter data leakage
      * **VALIDATION HOLDOUT :** Si méthode 'holdout' :
        - Split 70/30 train/test avec stratification
        - Entraîne sur train, évalue sur test
        - Calcule métriques standard (accuracy, precision, recall, f1)
      * **VALIDATION CROISÉE :** Si méthode 'cv' :
        - K-fold cross-validation (k=5) avec stratification
        - Calcule métriques pour chaque fold
        - Moyenne et écart-type des performances
      * **VALIDATION BOOTSTRAP :** Si méthode 'bootstrap' :
        - Échantillonnage avec remise (n=100 iterations)
        - Calcule distribution des métriques
        - Intervalles de confiance 95%
      * **TESTS STATISTIQUES :** Applique tests robustesse :
        - Test normalité des résidus
        - Test homoscédasticité
        - Test indépendance des erreurs
      * **MÉTRIQUES AVANCÉES :** Calcule indicateurs spécialisés :
        - AUC-ROC pour discrimination
        - Brier Score pour calibration
        - Log-loss pour probabilités
      * **ANALYSE STABILITÉ :** Évalue consistance :
        - Variance inter-folds pour CV
        - Stabilité bootstrap pour robustesse
        - Sensibilité aux outliers
      * **DÉTECTION OVERFITTING :** Compare :
        - Performance train vs validation
        - Complexité modèle vs généralisation
        - Learning curves pour diagnostic
      * **RECOMMANDATIONS :** Génère suggestions :
        - Ajustements hyperparamètres
        - Modifications architecture
        - Stratégies régularisation
      * **GESTION ERREURS :** Capture exceptions avec validation simplifiée
    - RETOUR : Dict[str, Any] - Résultats validation avec tests statistiques et recommandations
    - UTILITÉ : Validation rigoureuse performance pour confiance en production et optimisation

================================================================================
SECTION 3 : GESTION DONNEES
================================================================================

1. _prepare_training_data.txt (HybridBaccaratPredictor._prepare_training_data - Préparation données entraînement)
   - Lignes 1797-2058 dans hbp.py (262 lignes)
   - FONCTION : Prépare package complet de données pour entraînement avec features LGBM/LSTM, validation et échantillonnage
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * force_use_historical (bool, optionnel) - Force utilisation données historiques (défaut: False)
     * max_games (int, optionnel) - Limite nombre de jeux (défaut: None)
     * sampling_fraction (float, optionnel) - Fraction échantillonnage (défaut: None)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉTERMINATION SOURCE :** Choisit source données avec priorité :
       - Si `force_use_historical=True` et `self.loaded_historical=True`, utilise `self.historical_data`
       - Sinon utilise `self.sequence` (données session courante)
       - Log source choisie pour traçabilité
     * **VALIDATION DONNÉES :** Vérifie `if not data_source or len(data_source) < 10:` avec logging warning et retour None si insuffisant
     * **ÉCHANTILLONNAGE :** Si `max_games` spécifié et `len(data_source) > max_games` :
       - Prend derniers `max_games` éléments avec `data_source = data_source[-max_games:]`
       - Si `sampling_fraction` spécifié, échantillonne aléatoirement avec `sample_size = int(len(data_source) * sampling_fraction)`
     * **GÉNÉRATION FEATURES LGBM :** Appelle `X_lgbm_all, y_all = self.create_lgbm_features(data_source)` pour features complètes
     * **VALIDATION FEATURES LGBM :** Vérifie `if X_lgbm_all is None or y_all is None or len(X_lgbm_all) == 0:` avec logging erreur et retour None
     * **GÉNÉRATION FEATURES LSTM :** Appelle `X_lstm_all = self.create_lstm_features(data_source)` pour séquences temporelles
     * **VALIDATION FEATURES LSTM :** Teste `if X_lstm_all is None or len(X_lstm_all) == 0:` avec logging erreur et retour None
     * **VÉRIFICATION COHÉRENCE :** Assure `if len(X_lgbm_all) != len(X_lstm_all) or len(X_lgbm_all) != len(y_all):` avec logging erreur et retour None
     * **SPLIT TEMPOREL :** Utilise `split_index = int(len(X_lgbm_all) * 0.8)` pour division 80/20 train/validation temporelle
     * **INDICES SPLIT :** Crée `train_indices = list(range(split_index))` et `val_indices = list(range(split_index, len(X_lgbm_all)))`
     * **CALCUL POIDS :** Génère `sample_weights = np.ones(len(y_all))` avec pondération optionnelle récente
     * **VALIDATION FINALE :** Vérifie cohérence toutes dimensions et types de données
     * **PACKAGE RÉSULTAT :** Retourne tuple `(X_lgbm_all, y_all, X_lstm_all, sample_weights, train_indices, val_indices, data_source, metadata)`
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Package données préparé: {len(X_lgbm_all)} échantillons, {len(train_indices)} train, {len(val_indices)} validation")`
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour None
   - RETOUR : Tuple[Optional[...]] - Package complet données ou None si échec
   - UTILITÉ : Préparation robuste et cohérente des données pour entraînement avec validation et métadonnées

2. create_lgbm_features.txt (HybridBaccaratPredictor.create_lgbm_features - Création features LGBM)
   - Lignes 6614-6890 dans hbp.py (277 lignes)
   - FONCTION : Génère features LGBM sophistiquées avec patterns, statistiques et indicateurs techniques pour classification
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats pour extraction features
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence or len(sequence) < 3:` avec retour `(None, None)` si données insuffisantes
     * **CONVERSION NUMÉRIQUE :** Transforme séquence avec `numeric_sequence = [1 if outcome.lower() == 'player' else 0 for outcome in sequence]`
     * **INITIALISATION FEATURES :** Crée listes `X_features = []` et `y_labels = []` pour collecte
     * **GÉNÉRATION PAR POSITION :** Itère `for i in range(2, len(sequence)):` pour créer échantillon à chaque position :
       - **FEATURES BASIQUES :** Calcule ratios récents avec `recent_window = min(10, i)` puis `player_ratio = sum(numeric_sequence[i-recent_window:i]) / recent_window`
       - **STREAK FEATURES :** Détermine streak actuel avec comptage consécutif depuis position courante
       - **PATTERN FEATURES :** Extrait n-grams avec `if i >= 2: pattern_2gram = tuple(numeric_sequence[i-2:i])` et encode patterns fréquents
       - **FEATURES STATISTIQUES :** Calcule variance, écart-type, tendance sur fenêtre glissante
       - **FEATURES TEMPORELLES :** Ajoute position relative, cyclicité, momentum
     * **PATTERNS AVANCÉS :** Analyse patterns complexes :
       - Alternance avec `alternation_score = sum(1 for j in range(1, recent_window) if numeric_sequence[i-j] != numeric_sequence[i-j-1]) / max(1, recent_window-1)`
       - Clustering avec détection groupes homogènes
       - Entropie locale pour mesure prévisibilité
     * **FEATURES MARKOV :** Si modèle Markov disponible :
       - Probabilités Markov pour état suivant
       - Confiance prédiction Markov
       - Divergence avec distribution uniforme
     * **NORMALISATION :** Applique `np.clip()` pour borner features dans plages appropriées
     * **ASSEMBLAGE VECTEUR :** Combine toutes features en `feature_vector = [player_ratio, streak_length, pattern_score, ...]`
     * **VALIDATION VECTEUR :** Vérifie cohérence dimensions et absence NaN/Inf
     * **LABEL EXTRACTION :** Utilise `y_labels.append(1 if sequence[i].lower() == 'player' else 0)` pour target
     * **CONVERSION NUMPY :** Transforme en `X_features = np.array(X_features, dtype=np.float32)` et `y_labels = np.array(y_labels, dtype=np.int32)`
     * **VALIDATION FINALE :** Vérifie `X_features.shape[0] == len(y_labels)` pour cohérence
     * **GESTION ERREURS :** Capture exceptions avec logging et retour `(None, None)`
   - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray]] - (Features, Labels) ou (None, None) si échec
   - UTILITÉ : Extraction features riches pour modèle LGBM avec patterns sophistiqués et indicateurs techniques

3. create_lstm_features.txt (HybridBaccaratPredictor.create_lstm_features - Création features LSTM)
   - Lignes 6892-7068 dans hbp.py (177 lignes)
   - FONCTION : Génère features LSTM avec séquences temporelles multi-dimensionnelles pour réseaux de neurones
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats pour extraction features temporelles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence:` avec retour `None` si séquence vide
     * **PARAMÈTRES LSTM :** Récupère `sequence_length = self.config.lstm_sequence_length` et `input_size = self.config.lstm_input_size` depuis configuration
     * **VALIDATION LONGUEUR :** Teste `if len(sequence) < sequence_length:` avec gestion séquence courte via padding ou répétition
     * **CONVERSION NUMÉRIQUE :** Transforme `numeric_sequence = [1.0 if outcome.lower() == 'player' else 0.0 for outcome in sequence]`
     * **INITIALISATION FEATURES :** Crée `lstm_features = []` pour collecte séquences
     * **GÉNÉRATION SÉQUENCES :** Itère `for i in range(sequence_length, len(sequence)):` :
       - **EXTRACTION FENÊTRE :** Récupère `window = numeric_sequence[i-sequence_length:i]` pour séquence d'entrée
       - **FEATURES MULTI-DIM :** Pour chaque position dans fenêtre, calcule :
         - Valeur brute (0.0 ou 1.0)
         - Moyenne mobile locale
         - Écart à la moyenne
         - Gradient local (différence avec position précédente)
         - Position relative dans fenêtre (0.0 à 1.0)
     * **FEATURES CONTEXTUELLES :** Ajoute dimensions supplémentaires :
       - Streak length à chaque position
       - Distance au changement le plus proche
       - Entropie locale sur sous-fenêtre
       - Momentum (tendance directionnelle)
     * **NORMALISATION :** Applique normalisation par feature :
       - Z-score pour features continues
       - Min-max pour features bornées
       - Clipping pour éviter valeurs extrêmes
     * **ASSEMBLAGE MATRICE :** Crée `feature_matrix = np.array(multi_dim_features, dtype=np.float32)` avec shape `(sequence_length, input_size)`
     * **VALIDATION DIMENSIONS :** Vérifie `feature_matrix.shape == (sequence_length, input_size)` pour cohérence configuration
     * **GESTION SÉQUENCE COURTE :** Si `len(sequence) < sequence_length`, applique stratégies :
       - Padding avec valeurs moyennes
       - Répétition cyclique
       - Interpolation linéaire
     * **CONVERSION FINALE :** Transforme en `lstm_features = np.array(lstm_features, dtype=np.float32)` avec shape `(n_samples, sequence_length, input_size)`
     * **VALIDATION FINALE :** Vérifie absence NaN/Inf et cohérence dimensions
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour `None`
   - RETOUR : Optional[np.ndarray] - Features LSTM shape (n_samples, sequence_length, input_size) ou None si échec
   - UTILITÉ : Préparation séquences temporelles riches pour LSTM avec features multi-dimensionnelles et normalisation

4. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features - Création features hybrides)
   - Lignes 7070-7186 dans hbp.py (117 lignes)
   - FONCTION : Génère features pour prédiction hybride en combinant approches LGBM et LSTM avec optimisations
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats pour extraction features
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence:` avec retour `(None, None)` si séquence vide
     * **GÉNÉRATION LGBM :** Appelle `lgbm_features_full, _ = self.create_lgbm_features(sequence)` pour features complètes
     * **EXTRACTION DERNIÈRE :** Utilise `lgbm_features = lgbm_features_full[-1] if lgbm_features_full is not None and len(lgbm_features_full) > 0 else None` pour dernière observation
     * **GÉNÉRATION LSTM :** Appelle `lstm_features_full = self.create_lstm_features(sequence)` pour séquences temporelles
     * **EXTRACTION DERNIÈRE :** Utilise `lstm_features = lstm_features_full[-1] if lstm_features_full is not None and len(lstm_features_full) > 0 else None` pour dernière séquence
     * **GESTION ÉCHEC LGBM :** Si `lgbm_features is None` :
       - Génère features basiques avec `self._generate_basic_lgbm_features(sequence)` comme fallback
       - Calcule ratios simples, streaks, patterns élémentaires
     * **GESTION ÉCHEC LSTM :** Si `lstm_features is None` :
       - Appelle `self.handle_short_sequence(sequence)` pour gestion séquence courte
       - Crée matrice avec padding ou répétition selon configuration
     * **VALIDATION COHÉRENCE :** Vérifie dimensions attendues :
       - LGBM : vecteur 1D avec nombre features configuré
       - LSTM : matrice 2D (sequence_length, input_size)
     * **OPTIMISATIONS CACHE :** Utilise cache pour éviter recalculs :
       - Vérifie si features déjà calculées pour cette séquence
       - Stocke résultats dans cache LRU
     * **LOGGING DEBUG :** Enregistre `logger.debug(f"Features hybrides générées - LGBM: {lgbm_features.shape if lgbm_features is not None else None}, LSTM: {lstm_features.shape if lstm_features is not None else None}")` pour debugging
     * **GESTION ERREURS :** Capture exceptions avec fallback vers features basiques
   - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray]] - (Features LGBM, Features LSTM) ou (None, None) si échec total
   - UTILITÉ : Point d'entrée unifié pour génération features hybrides avec fallbacks robustes et optimisations cache

5. handle_short_sequence.txt (HybridBaccaratPredictor.handle_short_sequence - Gestion séquence courte)
   - Lignes 7188-7254 dans hbp.py (67 lignes)
   - FONCTION : Gère séquences trop courtes pour LSTM via padding, répétition ou interpolation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence courte à traiter
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence:` avec retour matrice zéros si séquence vide
     * **PARAMÈTRES CONFIG :** Récupère `required_length = self.config.lstm_sequence_length` et `input_size = self.config.lstm_input_size`
     * **CONVERSION NUMÉRIQUE :** Transforme `numeric_sequence = [1.0 if outcome.lower() == 'player' else 0.0 for outcome in sequence]`
     * **STRATÉGIE PADDING :** Si `len(sequence) < required_length` :
       - Calcule `padding_needed = required_length - len(sequence)`
       - Applique padding avec moyenne : `mean_value = np.mean(numeric_sequence)` puis `padded_sequence = [mean_value] * padding_needed + numeric_sequence`
     * **STRATÉGIE RÉPÉTITION :** Alternative avec répétition cyclique :
       - Répète séquence : `repeated_sequence = (numeric_sequence * ((required_length // len(sequence)) + 1))[:required_length]`
     * **GÉNÉRATION FEATURES :** Pour chaque position, crée vecteur multi-dimensionnel :
       - Valeur brute
       - Position relative (i / required_length)
       - Indicateur padding (1.0 si paddé, 0.0 sinon)
       - Moyenne locale sur fenêtre 3
     * **ASSEMBLAGE MATRICE :** Crée `feature_matrix = np.array(features, dtype=np.float32)` avec shape `(required_length, input_size)`
     * **VALIDATION DIMENSIONS :** Vérifie `feature_matrix.shape == (required_length, input_size)`
     * **GESTION ERREURS :** Capture exceptions avec retour matrice zéros par défaut
   - RETOUR : np.ndarray - Matrice features shape (sequence_length, input_size)
   - UTILITÉ : Gestion robuste séquences courtes pour compatibilité LSTM

6. _generate_basic_lgbm_features.txt (HybridBaccaratPredictor._generate_basic_lgbm_features - Features LGBM basiques)
   - Lignes 7256-7322 dans hbp.py (67 lignes)
   - FONCTION : Génère features LGBM basiques comme fallback en cas d'échec génération complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence pour extraction features basiques
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence or len(sequence) < 2:` avec retour vecteur zéros si insuffisant
     * **CONVERSION NUMÉRIQUE :** Transforme `numeric_sequence = [1 if outcome.lower() == 'player' else 0 for outcome in sequence]`
     * **FEATURES SIMPLES :** Calcule features élémentaires :
       - `player_ratio = sum(numeric_sequence) / len(numeric_sequence)` pour ratio global Player
       - `recent_ratio = sum(numeric_sequence[-5:]) / min(5, len(numeric_sequence))` pour ratio récent
       - `last_outcome = numeric_sequence[-1]` pour dernier résultat
     * **STREAK CALCULATION :** Détermine streak actuel :
       - Compte consécutifs depuis fin : `streak_length = 1` puis `while i > 0 and numeric_sequence[i] == numeric_sequence[i-1]: streak_length += 1; i -= 1`
       - Normalise : `normalized_streak = min(streak_length / 10.0, 1.0)`
     * **PATTERN SIMPLE :** Analyse pattern basique :
       - Alternance récente : `alternation = sum(1 for i in range(1, min(5, len(sequence))) if numeric_sequence[-i] != numeric_sequence[-i-1]) / 4.0`
     * **FEATURES STATISTIQUES :** Calcule :
       - Variance récente sur fenêtre 10
       - Tendance linéaire simple
       - Entropie basique
     * **ASSEMBLAGE VECTEUR :** Combine en `basic_features = [player_ratio, recent_ratio, last_outcome, normalized_streak, alternation, variance, trend, entropy]`
     * **PADDING DIMENSIONS :** Si vecteur trop court, complète avec zéros pour atteindre dimension attendue
     * **VALIDATION FINALE :** Vérifie absence NaN/Inf et remplace par valeurs par défaut
     * **GESTION ERREURS :** Capture exceptions avec vecteur zéros de dimension appropriée
   - RETOUR : np.ndarray - Vecteur features basiques de dimension fixe
   - UTILITÉ : Fallback robuste pour génération features en cas d'échec méthode principale

7. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data - Chargement données historiques)
   - Lignes 1589-1655 dans hbp.py (67 lignes)
   - FONCTION : Charge données historiques depuis fichier pour entraînement avec validation et nettoyage
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * file_path (str) - Chemin vers fichier de données historiques
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CHEMIN :** Vérifie `if not file_path or not os.path.exists(file_path):` avec logging erreur et retour False si fichier inexistant
     * **DÉTECTION FORMAT :** Détermine format fichier via extension :
       - `.json` : Format JSON avec structure définie
       - `.csv` : Format CSV avec colonnes spécifiques
       - `.txt` : Format texte simple ligne par ligne
     * **CHARGEMENT JSON :** Si format JSON :
       - Utilise `with open(file_path, 'r', encoding='utf-8') as f: data = json.load(f)`
       - Extrait séquence depuis clé 'sequence' ou 'data'
       - Valide structure attendue
     * **CHARGEMENT CSV :** Si format CSV :
       - Utilise pandas si disponible : `df = pd.read_csv(file_path)`
       - Extrait colonne 'outcome' ou première colonne
       - Gère headers et séparateurs
     * **CHARGEMENT TXT :** Si format texte :
       - Lit ligne par ligne : `with open(file_path, 'r') as f: lines = f.readlines()`
       - Nettoie et valide chaque ligne
       - Filtre lignes vides et commentaires
     * **NETTOYAGE DONNÉES :** Normalise valeurs :
       - Convertit en minuscules : `outcome.lower().strip()`
       - Valide valeurs : `if outcome in ['player', 'banker']:`
       - Filtre valeurs invalides avec logging
     * **VALIDATION QUALITÉ :** Vérifie cohérence :
       - Longueur minimale : `if len(cleaned_data) < 50:`
       - Distribution équilibrée : ratio Player/Banker dans plage acceptable
       - Absence patterns suspects (trop réguliers)
     * **STOCKAGE :** Assigne `self.historical_data = cleaned_data` et `self.loaded_historical = True`
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Données historiques chargées: {len(cleaned_data)} échantillons depuis {file_path}")`
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour False
   - RETOUR : bool - True si chargement réussi, False sinon
   - UTILITÉ : Chargement robuste données historiques avec validation qualité pour entraînement

8. save_historical_data.txt (HybridBaccaratPredictor.save_historical_data - Sauvegarde données historiques)
   - Lignes 1657-1723 dans hbp.py (67 lignes)
   - FONCTION : Sauvegarde données historiques vers fichier avec formatage et compression optionnelle
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * file_path (str) - Chemin fichier de sortie
     * format_type (str, optionnel) - Format de sortie ('json', 'csv', 'txt') (défaut: 'json')
     * compress (bool, optionnel) - Compression gzip (défaut: False)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Vérifie `if not self.historical_data:` avec logging warning et retour False si aucune donnée
     * **PRÉPARATION DONNÉES :** Combine données historiques et session courante :
       - `all_data = self.historical_data + self.sequence` pour dataset complet
       - Déduplique si nécessaire
       - Valide cohérence
     * **SAUVEGARDE JSON :** Si format JSON :
       - Structure avec métadonnées : `data = {'sequence': all_data, 'timestamp': datetime.now().isoformat(), 'count': len(all_data)}`
       - Utilise `json.dump(data, f, indent=2, ensure_ascii=False)` pour lisibilité
     * **SAUVEGARDE CSV :** Si format CSV :
       - Crée DataFrame : `df = pd.DataFrame({'outcome': all_data})`
       - Ajoute colonnes métadonnées (index, timestamp)
       - Utilise `df.to_csv(file_path, index=False)`
     * **SAUVEGARDE TXT :** Si format texte :
       - Écrit ligne par ligne : `f.write(f"{outcome}\n" for outcome in all_data)`
       - Ajoute header avec métadonnées en commentaire
     * **COMPRESSION :** Si `compress=True` :
       - Utilise gzip : `with gzip.open(f"{file_path}.gz", 'wt') as f:`
       - Supprime fichier original non compressé
       - Met à jour chemin de retour
     * **VALIDATION ÉCRITURE :** Vérifie fichier créé avec taille cohérente
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Données sauvegardées: {len(all_data)} échantillons vers {file_path}")`
     * **GESTION ERREURS :** Capture exceptions avec logging et retour False
   - RETOUR : bool - True si sauvegarde réussie, False sinon
   - UTILITÉ : Persistance données pour réutilisation et archivage avec formats multiples

9. validate_data_quality.txt (HybridBaccaratPredictor.validate_data_quality - Validation qualité données)
   - Lignes 1725-1795 dans hbp.py (71 lignes)
   - FONCTION : Valide qualité des données avec tests statistiques et détection anomalies
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * data (List[str], optionnel) - Données à valider (défaut: self.sequence)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SÉLECTION DONNÉES :** Utilise `data = data or self.sequence` avec validation `if not data:`
     * **CONVERSION NUMÉRIQUE :** Transforme `numeric_data = [1 if outcome.lower() == 'player' else 0 for outcome in data]`
     * **TESTS BASIQUES :** Vérifie critères fondamentaux :
       - Longueur minimale : `if len(data) < 10:`
       - Valeurs valides : toutes dans ['player', 'banker']
       - Absence doublons consécutifs suspects
     * **ANALYSE DISTRIBUTION :** Calcule statistiques :
       - `player_ratio = sum(numeric_data) / len(numeric_data)` pour proportion Player
       - Test équilibre : `if not 0.3 <= player_ratio <= 0.7:` warning déséquilibre
       - Entropie : `entropy = -p*log2(p) - (1-p)*log2(1-p)` pour mesure aléatoire
     * **DÉTECTION PATTERNS SUSPECTS :** Identifie anomalies :
       - Alternance excessive : `alternation_rate = sum(1 for i in range(1, len(data)) if numeric_data[i] != numeric_data[i-1]) / (len(data)-1)`
       - Streaks anormaux : longueurs maximales vs distribution attendue
       - Périodicité suspecte via autocorrélation
     * **TESTS STATISTIQUES :** Applique tests robustesse :
       - Test runs pour aléatoire
       - Test Chi-carré pour distribution
       - Test Kolmogorov-Smirnov vs distribution théorique
     * **SCORE QUALITÉ :** Calcule score composite :
       - Pondère différents critères (distribution, aléatoire, cohérence)
       - Normalise entre 0.0 (mauvais) et 1.0 (excellent)
       - Seuils : >0.8 excellent, 0.6-0.8 bon, 0.4-0.6 acceptable, <0.4 problématique
     * **RECOMMANDATIONS :** Génère suggestions amélioration :
       - Augmentation taille échantillon si trop petit
       - Vérification source si patterns suspects
       - Nettoyage si valeurs aberrantes
     * **LOGGING RÉSULTATS :** Enregistre score et détails pour monitoring
     * **GESTION ERREURS :** Capture exceptions avec score 0.0 par défaut
   - RETOUR : Dict[str, Any] - Score qualité et détails analyse
   - UTILITÉ : Validation robuste qualité données pour fiabilité entraînement et prédictions

10. add_outcome.txt (HybridBaccaratPredictor.add_outcome - Ajout résultat)
    - Lignes 1479-1509 dans hbp.py (31 lignes)
    - FONCTION : Ajoute nouveau résultat à la séquence avec validation et mise à jour des structures internes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * outcome (str) - Résultat à ajouter ('player' ou 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION OUTCOME :** Vérifie `if not outcome or not isinstance(outcome, str):` avec logging warning et retour si invalide
      * **NORMALISATION :** Applique `outcome = outcome.lower().strip()` pour standardisation
      * **VALIDATION VALEUR :** Teste `if outcome not in ['player', 'banker']:` avec logging warning et retour si valeur incorrecte
      * **AJOUT SÉQUENCE :** Ajoute à `self.sequence.append(outcome)` pour mise à jour séquence principale
      * **LIMITATION TAILLE :** Si `len(self.sequence) > self.config.max_sequence_length:` applique `self.sequence = self.sequence[-self.config.max_sequence_length:]` pour fenêtre glissante
      * **MISE À JOUR MARKOV :** Si modèle Markov disponible, appelle `self.markov.add_outcome(outcome)` pour mise à jour transitions
      * **INVALIDATION CACHE :** Nettoie caches features avec `self._invalidate_feature_cache()` pour forcer recalcul
      * **LOGGING DEBUG :** Enregistre `logger.debug(f"Résultat ajouté: {outcome}, séquence length: {len(self.sequence)}")` pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec logging détaillé
    - RETOUR : None - Met à jour directement les structures internes
    - UTILITÉ : Point d'entrée principal pour mise à jour séquence avec validation et cohérence

11. get_sequence_stats.txt (HybridBaccaratPredictor.get_sequence_stats - Statistiques séquence)
    - Lignes 1511-1541 dans hbp.py (31 lignes)
    - FONCTION : Calcule statistiques descriptives de la séquence courante
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Vérifie `if not self.sequence:` avec retour dictionnaire vide si aucune donnée
      * **CONVERSION NUMÉRIQUE :** Transforme `numeric_sequence = [1 if outcome == 'player' else 0 for outcome in self.sequence]`
      * **STATISTIQUES BASIQUES :** Calcule :
        - `total_games = len(self.sequence)` pour nombre total
        - `player_count = sum(numeric_sequence)` pour comptage Player
        - `banker_count = total_games - player_count` pour comptage Banker
        - `player_ratio = player_count / total_games` pour proportion Player
      * **STREAK ANALYSIS :** Détermine streaks :
        - Streak actuel avec comptage depuis fin
        - Streak maximum dans séquence complète
        - Distribution longueurs streaks
      * **PATTERNS :** Analyse patterns :
        - Taux alternance global
        - Patterns 2-grams et 3-grams fréquents
        - Entropie séquence pour mesure aléatoire
      * **TENDANCES :** Calcule évolution :
        - Tendance récente (derniers 20 résultats)
        - Momentum directionnel
        - Variance glissante
      * **ASSEMBLAGE RÉSULTAT :** Structure dictionnaire avec toutes statistiques
      * **GESTION ERREURS :** Capture exceptions avec statistiques par défaut
    - RETOUR : Dict[str, Any] - Statistiques complètes de la séquence
    - UTILITÉ : Analyse descriptive séquence pour monitoring et diagnostic

12. clear_sequence.txt (HybridBaccaratPredictor.clear_sequence - Effacement séquence)
    - Lignes 1543-1563 dans hbp.py (21 lignes)
    - FONCTION : Efface séquence courante et réinitialise structures associées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EFFACEMENT SÉQUENCE :** Vide `self.sequence = []` pour reset complet
      * **RESET MARKOV :** Si modèle Markov disponible, appelle `self.markov.reset()` pour réinitialisation
      * **NETTOYAGE CACHE :** Appelle `self._invalidate_feature_cache()` pour purge cache features
      * **RESET HISTORIQUE :** Vide `self.prediction_history = []` pour nouveau départ
      * **RESET PERFORMANCE :** Réinitialise `self.method_performance` avec compteurs zéro
      * **LOGGING :** Enregistre `logger.info("Séquence et historique effacés")` pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec logging
    - RETOUR : None - Réinitialise directement les structures
    - UTILITÉ : Reset complet pour nouvelle session ou debugging

13. _invalidate_feature_cache.txt (HybridBaccaratPredictor._invalidate_feature_cache - Invalidation cache features)
    - Lignes 1565-1579 dans hbp.py (15 lignes)
    - FONCTION : Invalide caches features pour forcer recalcul lors prochaine utilisation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CACHE LGBM :** Vide `self._lgbm_cache = {}` si existe
      * **CACHE LSTM :** Vide `self._lstm_cache = {}` si existe
      * **CACHE HYBRIDE :** Vide `self._hybrid_cache = {}` si existe
      * **TIMESTAMPS :** Reset `self._cache_timestamps = {}` pour tracking fraîcheur
      * **LOGGING DEBUG :** Enregistre invalidation pour debugging
      * **GESTION ERREURS :** Capture exceptions silencieusement
    - RETOUR : None - Invalide directement les caches
    - UTILITÉ : Gestion cache pour cohérence features après modifications séquence

14. export_sequence_data.txt (HybridBaccaratPredictor.export_sequence_data - Export données séquence)
    - Lignes 7324-7384 dans hbp.py (61 lignes)
    - FONCTION : Exporte données séquence vers formats externes avec métadonnées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * file_path (str) - Chemin fichier de sortie
      * include_features (bool, optionnel) - Inclure features calculées (défaut: False)
      * format_type (str, optionnel) - Format export ('json', 'csv') (défaut: 'json')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Vérifie `if not self.sequence:` avec logging warning et retour False si vide
      * **PRÉPARATION DONNÉES :** Collecte données export :
        - Séquence principale
        - Statistiques descriptives via `self.get_sequence_stats()`
        - Métadonnées (timestamp, version, configuration)
      * **FEATURES OPTIONNELLES :** Si `include_features=True` :
        - Génère features LGBM via `self.create_lgbm_features(self.sequence)`
        - Génère features LSTM via `self.create_lstm_features(self.sequence)`
        - Inclut dans export avec descriptions
      * **EXPORT JSON :** Si format JSON :
        - Structure hiérarchique avec sections logiques
        - Métadonnées complètes et lisibles
        - Indentation pour lisibilité humaine
      * **EXPORT CSV :** Si format CSV :
        - Table principale avec séquence et index
        - Tables secondaires pour statistiques
        - Headers descriptifs
      * **VALIDATION EXPORT :** Vérifie fichier créé avec taille appropriée
      * **LOGGING SUCCÈS :** Enregistre détails export pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour False
    - RETOUR : bool - True si export réussi, False sinon
    - UTILITÉ : Export données pour analyse externe et archivage

15. import_sequence_data.txt (HybridBaccaratPredictor.import_sequence_data - Import données séquence)
    - Lignes 7386-7446 dans hbp.py (61 lignes)
    - FONCTION : Importe données séquence depuis fichier externe avec validation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * file_path (str) - Chemin fichier source
      * replace_current (bool, optionnel) - Remplacer séquence courante (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FICHIER :** Vérifie `if not os.path.exists(file_path):` avec logging erreur et retour False
      * **DÉTECTION FORMAT :** Détermine format via extension fichier
      * **IMPORT JSON :** Si format JSON :
        - Charge avec `json.load()` et validation structure
        - Extrait séquence depuis clé appropriée
        - Valide métadonnées compatibilité
      * **IMPORT CSV :** Si format CSV :
        - Utilise pandas si disponible
        - Extrait colonne séquence
        - Gère headers et formats divers
      * **VALIDATION DONNÉES :** Vérifie qualité données importées :
        - Valeurs valides ('player'/'banker')
        - Longueur raisonnable
        - Cohérence statistique
      * **INTÉGRATION :** Selon `replace_current` :
        - Si True : remplace `self.sequence = imported_data`
        - Si False : étend `self.sequence.extend(imported_data)`
      * **MISE À JOUR STRUCTURES :** Met à jour modèles et caches :
        - Réentraîne Markov si nécessaire
        - Invalide caches features
        - Reset historique prédictions
      * **LOGGING SUCCÈS :** Enregistre détails import pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec logging et retour False
    - RETOUR : bool - True si import réussi, False sinon
    - UTILITÉ : Chargement données externes pour continuité sessions ou tests

16. get_data_summary.txt (HybridBaccaratPredictor.get_data_summary - Résumé données)
    - Lignes 7448-7508 dans hbp.py (61 lignes)
    - FONCTION : Génère résumé complet des données disponibles (séquence + historique)
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **COLLECTE SOURCES :** Rassemble toutes sources données :
        - Séquence courante `self.sequence`
        - Données historiques `self.historical_data` si chargées
        - Historique prédictions `self.prediction_history`
      * **STATISTIQUES SÉQUENCE :** Calcule métriques séquence courante :
        - Longueur, distribution Player/Banker
        - Streaks, patterns, entropie
        - Qualité données via `self.validate_data_quality()`
      * **STATISTIQUES HISTORIQUES :** Si données historiques disponibles :
        - Métriques similaires sur dataset historique
        - Comparaison distributions séquence vs historique
        - Cohérence temporelle
      * **MÉTRIQUES PRÉDICTIONS :** Analyse historique prédictions :
        - Nombre prédictions par type
        - Performance globale et par méthode
        - Évolution dans le temps
      * **CAPACITÉS SYSTÈME :** Évalue capacités actuelles :
        - Modèles disponibles et entraînés
        - Qualité features générables
        - Recommandations amélioration
      * **DIAGNOSTIC SANTÉ :** Identifie problèmes potentiels :
        - Données insuffisantes
        - Déséquilibres distribution
        - Dégradation performance
      * **ASSEMBLAGE RÉSUMÉ :** Structure résultat avec sections logiques
      * **GESTION ERREURS :** Capture exceptions avec résumé partiel
    - RETOUR : Dict[str, Any] - Résumé complet état données système
    - UTILITÉ : Vue d'ensemble santé données pour monitoring et diagnostic

17. backup_sequence_data.txt (HybridBaccaratPredictor.backup_sequence_data - Sauvegarde données séquence)
    - Lignes 7510-7570 dans hbp.py (61 lignes)
    - FONCTION : Crée sauvegarde automatique des données séquence avec rotation et compression
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * backup_dir (str, optionnel) - Répertoire sauvegarde (défaut: './backups')
      * max_backups (int, optionnel) - Nombre max sauvegardes (défaut: 10)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if not self.sequence:` avec logging warning et retour False si aucune donnée
      * **CRÉATION RÉPERTOIRE :** Assure existence avec `os.makedirs(backup_dir, exist_ok=True)` si nécessaire
      * **GÉNÉRATION NOM :** Crée nom unique avec `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` puis `backup_name = f"sequence_backup_{timestamp}.json.gz"`
      * **PRÉPARATION DONNÉES :** Structure données sauvegarde :
        - Séquence complète avec métadonnées
        - Statistiques descriptives
        - Configuration système
        - Hash intégrité pour validation
      * **COMPRESSION :** Utilise gzip pour optimiser espace :
        - `with gzip.open(backup_path, 'wt', encoding='utf-8') as f:`
        - `json.dump(backup_data, f, indent=2)`
      * **ROTATION SAUVEGARDES :** Gère limite nombre sauvegardes :
        - Liste fichiers backup existants
        - Trie par date création
        - Supprime plus anciens si dépasse `max_backups`
      * **VALIDATION SAUVEGARDE :** Vérifie intégrité :
        - Taille fichier cohérente
        - Lecture test pour validation format
        - Vérification hash intégrité
      * **LOGGING SUCCÈS :** Enregistre détails sauvegarde pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec nettoyage fichier partiel
    - RETOUR : str - Chemin sauvegarde créée ou None si échec
    - UTILITÉ : Protection données avec sauvegardes automatiques et rotation

18. restore_sequence_data.txt (HybridBaccaratPredictor.restore_sequence_data - Restauration données séquence)
    - Lignes 7572-7632 dans hbp.py (61 lignes)
    - FONCTION : Restaure données séquence depuis sauvegarde avec validation intégrité
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * backup_path (str) - Chemin vers fichier sauvegarde
      * verify_integrity (bool, optionnel) - Vérifier intégrité (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FICHIER :** Vérifie `if not os.path.exists(backup_path):` avec logging erreur et retour False
      * **DÉTECTION COMPRESSION :** Détermine si fichier compressé via extension `.gz`
      * **CHARGEMENT DONNÉES :** Selon format :
        - Si compressé : `with gzip.open(backup_path, 'rt', encoding='utf-8') as f:`
        - Si normal : `with open(backup_path, 'r', encoding='utf-8') as f:`
        - Parse JSON avec validation structure
      * **VÉRIFICATION INTÉGRITÉ :** Si `verify_integrity=True` :
        - Calcule hash données chargées
        - Compare avec hash stocké dans sauvegarde
        - Valide cohérence métadonnées
      * **VALIDATION DONNÉES :** Vérifie qualité données restaurées :
        - Format séquence correct
        - Valeurs valides ('player'/'banker')
        - Longueur raisonnable
      * **RESTAURATION :** Remplace données courantes :
        - `self.sequence = restored_sequence`
        - Met à jour structures associées
        - Invalide caches features
      * **MISE À JOUR MODÈLES :** Réentraîne si nécessaire :
        - Modèle Markov avec nouvelles données
        - Reset historique prédictions
        - Recalcul statistiques
      * **LOGGING SUCCÈS :** Enregistre détails restauration pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec préservation état original
    - RETOUR : bool - True si restauration réussie, False sinon
    - UTILITÉ : Récupération données depuis sauvegardes avec validation intégrité

19. optimize_data_storage.txt (HybridBaccaratPredictor.optimize_data_storage - Optimisation stockage données)
    - Lignes 7634-7694 dans hbp.py (61 lignes)
    - FONCTION : Optimise stockage données en mémoire avec compression et nettoyage
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * aggressive (bool, optionnel) - Optimisation agressive (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE CACHES :** Purge caches inutilisés :
        - Invalide caches features anciens
        - Supprime données temporaires
        - Nettoie historique prédictions ancien
      * **COMPRESSION SÉQUENCE :** Si séquence très longue :
        - Garde fenêtre récente active
        - Archive ancienne partie si nécessaire
        - Maintient continuité pour modèles
      * **OPTIMISATION HISTORIQUE :** Nettoie historique prédictions :
        - Garde dernières N prédictions selon config
        - Archive ou supprime anciennes selon `aggressive`
        - Préserve métriques importantes
      * **COMPRESSION MÉMOIRE :** Si `aggressive=True` :
        - Force garbage collection avec `gc.collect()`
        - Optimise structures données
        - Réduit précision si acceptable
      * **RÉORGANISATION DONNÉES :** Optimise layout mémoire :
        - Réorganise listes pour accès séquentiel
        - Convertit types pour efficacité
        - Élimine redondances
      * **CALCUL GAINS :** Mesure réduction mémoire :
        - Taille avant/après optimisation
        - Pourcentage gain espace
        - Impact performance estimé
      * **MISE À JOUR CONFIGURATION :** Ajuste paramètres selon optimisation :
        - Tailles caches adaptées
        - Seuils nettoyage automatique
        - Fréquence optimisation
      * **LOGGING RÉSULTATS :** Enregistre gains optimisation pour monitoring
      * **GESTION ERREURS :** Capture exceptions avec préservation fonctionnalité
    - RETOUR : Dict[str, Any] - Statistiques optimisation (gains mémoire, temps, etc.)
    - UTILITÉ : Gestion efficace mémoire pour sessions longues et gros volumes

20. get_memory_usage.txt (HybridBaccaratPredictor.get_memory_usage - Usage mémoire)
    - Lignes 7696-7756 dans hbp.py (61 lignes)
    - FONCTION : Analyse usage mémoire détaillé des composants système
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * detailed (bool, optionnel) - Analyse détaillée (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MESURE GLOBALE :** Utilise `psutil` si disponible pour mémoire processus :
        - RSS (Resident Set Size) pour mémoire physique
        - VMS (Virtual Memory Size) pour mémoire virtuelle
        - Pourcentage mémoire système utilisée
      * **ANALYSE COMPOSANTS :** Mesure taille objets principaux :
        - `sys.getsizeof(self.sequence)` pour séquence
        - `sys.getsizeof(self.historical_data)` pour données historiques
        - `sys.getsizeof(self.prediction_history)` pour historique prédictions
      * **ANALYSE MODÈLES :** Si `detailed=True`, mesure modèles :
        - Taille modèle LGBM en mémoire
        - Taille modèle LSTM/TensorFlow
        - Taille modèle Markov et transitions
      * **ANALYSE CACHES :** Mesure caches features :
        - Cache LGBM avec nombre entrées et taille
        - Cache LSTM avec dimensions
        - Cache hybride et métadonnées
      * **CALCUL RÉPARTITION :** Détermine répartition mémoire :
        - Pourcentage par composant
        - Identification gros consommateurs
        - Recommandations optimisation
      * **TENDANCES :** Si historique disponible :
        - Évolution usage dans le temps
        - Détection fuites mémoire
        - Prédiction croissance
      * **SEUILS ALERTE :** Compare avec limites configurées :
        - Alerte si usage > seuil warning
        - Critique si usage > seuil critique
        - Suggestions actions correctives
      * **ASSEMBLAGE RAPPORT :** Structure résultat avec métriques clés
      * **GESTION ERREURS :** Capture exceptions avec métriques partielles
    - RETOUR : Dict[str, Any] - Analyse complète usage mémoire avec recommandations
    - UTILITÉ : Monitoring mémoire pour optimisation et détection problèmes

21. cleanup_old_data.txt (HybridBaccaratPredictor.cleanup_old_data - Nettoyage anciennes données)
    - Lignes 7758-7818 dans hbp.py (61 lignes)
    - FONCTION : Nettoie anciennes données selon politiques de rétention configurées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * max_age_days (int, optionnel) - Âge maximum en jours (défaut: 30)
      * keep_recent_count (int, optionnel) - Nombre récents à conserver (défaut: 1000)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE ÂGE DONNÉES :** Détermine âge données disponibles :
        - Timestamps historique prédictions
        - Date dernière mise à jour séquence
        - Âge sauvegardes et fichiers temporaires
      * **NETTOYAGE HISTORIQUE :** Applique politique rétention :
        - Garde `keep_recent_count` prédictions récentes
        - Supprime prédictions > `max_age_days` jours
        - Préserve métriques importantes même anciennes
      * **NETTOYAGE CACHES :** Purge caches expirés :
        - Vérifie timestamps cache features
        - Supprime entrées non utilisées récemment
        - Maintient cache actif pour performance
      * **NETTOYAGE FICHIERS :** Nettoie fichiers temporaires :
        - Sauvegardes anciennes selon rotation
        - Logs anciens selon configuration
        - Fichiers export temporaires
      * **ARCHIVAGE OPTIONNEL :** Avant suppression :
        - Archive données importantes si configuré
        - Compression pour économie espace
        - Métadonnées pour traçabilité
      * **CALCUL GAINS :** Mesure espace libéré :
        - Taille données supprimées
        - Réduction usage mémoire
        - Amélioration performance estimée
      * **MISE À JOUR INDICES :** Réorganise structures après nettoyage :
        - Recompacte listes et dictionnaires
        - Met à jour indices et références
        - Valide cohérence données restantes
      * **LOGGING NETTOYAGE :** Enregistre détails opération pour audit
      * **GESTION ERREURS :** Capture exceptions avec préservation données critiques
    - RETOUR : Dict[str, Any] - Statistiques nettoyage (éléments supprimés, espace libéré)
    - UTILITÉ : Maintenance automatique données pour performance et gestion espace

22. validate_data_integrity.txt (HybridBaccaratPredictor.validate_data_integrity - Validation intégrité données)
    - Lignes 7820-7880 dans hbp.py (61 lignes)
    - FONCTION : Valide intégrité complète des données système avec tests exhaustifs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * fix_issues (bool, optionnel) - Corriger problèmes détectés (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Vérifie cohérence séquence principale :
        - Valeurs valides uniquement ('player'/'banker')
        - Absence doublons ou valeurs corrompues
        - Continuité temporelle si timestamps disponibles
      * **VALIDATION HISTORIQUE :** Contrôle données historiques :
        - Format cohérent avec séquence courante
        - Absence conflits ou incohérences
        - Intégrité références croisées
      * **VALIDATION PRÉDICTIONS :** Vérifie historique prédictions :
        - Correspondance prédictions/résultats
        - Cohérence timestamps et métadonnées
        - Intégrité métriques calculées
      * **VALIDATION MODÈLES :** Contrôle état modèles :
        - Cohérence paramètres/données entraînement
        - Intégrité poids et structures
        - Synchronisation avec données courantes
      * **TESTS CROISÉS :** Vérifie cohérence inter-composants :
        - Statistiques vs données brutes
        - Caches vs calculs directs
        - Métriques vs historique
      * **DÉTECTION CORRUPTION :** Identifie données corrompues :
        - Checksums et hashes si disponibles
        - Patterns suspects ou impossibles
        - Incohérences statistiques
      * **CORRECTION AUTOMATIQUE :** Si `fix_issues=True` :
        - Supprime données corrompues
        - Recalcule métriques incohérentes
        - Resynchronise composants
      * **RAPPORT INTÉGRITÉ :** Génère rapport détaillé :
        - Problèmes détectés par catégorie
        - Corrections appliquées
        - Recommandations actions manuelles
      * **GESTION ERREURS :** Capture exceptions avec diagnostic détaillé
    - RETOUR : Dict[str, Any] - Rapport intégrité avec problèmes détectés et corrections
    - UTILITÉ : Validation exhaustive intégrité pour fiabilité système et détection corruption

23. synchronize_data_sources.txt (HybridBaccaratPredictor.synchronize_data_sources - Synchronisation sources données)
    - Lignes 7882-7942 dans hbp.py (61 lignes)
    - FONCTION : Synchronise différentes sources de données pour cohérence globale système
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_sync (bool, optionnel) - Force synchronisation complète (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE SOURCES :** Identifie toutes sources données disponibles :
        - Séquence courante en mémoire
        - Données historiques chargées
        - Historique prédictions
        - Caches features et modèles
      * **DÉTECTION DÉSYNCHRONISATION :** Identifie incohérences :
        - Timestamps différents entre sources
        - Longueurs séquences incohérentes
        - Métriques calculées divergentes
        - Références croisées brisées
      * **STRATÉGIE SYNCHRONISATION :** Détermine approche selon `force_sync` :
        - Si False : synchronisation incrémentale des différences
        - Si True : resynchronisation complète depuis source maître
      * **RÉSOLUTION CONFLITS :** Gère conflits entre sources :
        - Priorité à la source la plus récente
        - Validation croisée pour détection erreurs
        - Sauvegarde état avant modification
      * **MISE À JOUR SÉQUENCE :** Synchronise séquence principale :
        - Intègre nouveaux éléments depuis historique
        - Valide continuité temporelle
        - Met à jour indices et références
      * **MISE À JOUR MODÈLES :** Resynchronise modèles :
        - Réentraîne si données significativement changées
        - Met à jour poids et paramètres
        - Invalide caches obsolètes
      * **MISE À JOUR MÉTRIQUES :** Recalcule métriques affectées :
        - Statistiques descriptives
        - Performance historique
        - Indicateurs qualité
      * **VALIDATION SYNCHRONISATION :** Vérifie cohérence post-sync :
        - Tests intégrité sur toutes sources
        - Validation croisée métriques
        - Confirmation absence conflits
      * **LOGGING SYNCHRONISATION :** Enregistre détails opération pour audit
      * **GESTION ERREURS :** Capture exceptions avec rollback si nécessaire
    - RETOUR : Dict[str, Any] - Rapport synchronisation avec changements appliqués
    - UTILITÉ : Maintien cohérence données multi-sources pour fiabilité système

24. create_data_snapshot.txt (HybridBaccaratPredictor.create_data_snapshot - Création snapshot données)
    - Lignes 7944-8004 dans hbp.py (61 lignes)
    - FONCTION : Crée snapshot complet état données système pour sauvegarde ou analyse
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * include_models (bool, optionnel) - Inclure état modèles (défaut: True)
      * compress (bool, optionnel) - Compresser snapshot (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **COLLECTE DONNÉES :** Rassemble toutes données système :
        - Séquence courante complète
        - Données historiques si chargées
        - Historique prédictions avec métadonnées
        - Configuration système actuelle
      * **COLLECTE MODÈLES :** Si `include_models=True` :
        - État modèle Markov (transitions, probabilités)
        - Paramètres modèle LGBM (poids, hyperparamètres)
        - Architecture LSTM (si applicable)
        - Poids adaptatifs et performance
      * **COLLECTE MÉTRIQUES :** Inclut métriques calculées :
        - Statistiques descriptives
        - Performance par méthode
        - Métriques confiance et stabilité
        - Historique optimisations
      * **MÉTADONNÉES SNAPSHOT :** Ajoute informations contextuelles :
        - Timestamp création
        - Version système
        - Hash intégrité données
        - Conditions création (automatique/manuelle)
      * **SÉRIALISATION :** Convertit en format portable :
        - JSON pour données structurées
        - Pickle pour objets complexes si nécessaire
        - Base64 pour données binaires
      * **COMPRESSION :** Si `compress=True` :
        - Compression gzip pour réduction taille
        - Optimisation selon type données
        - Préservation intégrité
      * **VALIDATION SNAPSHOT :** Vérifie intégrité :
        - Complétude données collectées
        - Cohérence interne snapshot
        - Possibilité restauration
      * **GÉNÉRATION IDENTIFIANT :** Crée ID unique snapshot :
        - Timestamp + hash contenu
        - Facilite identification et tri
      * **LOGGING CRÉATION :** Enregistre détails snapshot pour traçabilité
      * **GESTION ERREURS :** Capture exceptions avec nettoyage ressources
    - RETOUR : Dict[str, Any] - Snapshot complet avec métadonnées et identifiant
    - UTILITÉ : Sauvegarde état complet pour restauration, analyse ou archivage

25. load_data_snapshot.txt (HybridBaccaratPredictor.load_data_snapshot - Chargement snapshot données)
    - Lignes 8006-8066 dans hbp.py (61 lignes)
    - FONCTION : Charge et restaure état système depuis snapshot avec validation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * snapshot_data (Dict[str, Any]) - Données snapshot à charger
      * restore_models (bool, optionnel) - Restaurer modèles (défaut: True)
      * verify_integrity (bool, optionnel) - Vérifier intégrité (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SNAPSHOT :** Vérifie format et intégrité :
        - Structure snapshot conforme
        - Présence sections obligatoires
        - Cohérence métadonnées
      * **VÉRIFICATION INTÉGRITÉ :** Si `verify_integrity=True` :
        - Calcule hash données chargées
        - Compare avec hash stocké
        - Valide checksums sections
      * **DÉCOMPRESSION :** Si snapshot compressé :
        - Décompression gzip
        - Validation format post-décompression
        - Gestion erreurs corruption
      * **RESTAURATION DONNÉES :** Charge données principales :
        - Remplace séquence courante
        - Intègre données historiques
        - Restaure historique prédictions
        - Met à jour configuration
      * **RESTAURATION MODÈLES :** Si `restore_models=True` :
        - Restaure état modèle Markov
        - Recharge paramètres LGBM
        - Reconstruit architecture LSTM
        - Restaure poids adaptatifs
      * **RESTAURATION MÉTRIQUES :** Recharge métriques calculées :
        - Statistiques descriptives
        - Performance historique
        - Métriques confiance
      * **VALIDATION POST-CHARGEMENT :** Vérifie cohérence :
        - Intégrité données restaurées
        - Fonctionnement modèles
        - Cohérence métriques
      * **MISE À JOUR STRUCTURES :** Synchronise structures internes :
        - Invalide caches obsolètes
        - Met à jour indices et références
        - Resynchronise composants
      * **LOGGING RESTAURATION :** Enregistre détails opération pour audit
      * **GESTION ERREURS :** Capture exceptions avec préservation état original
    - RETOUR : bool - True si chargement réussi, False sinon
    - UTILITÉ : Restauration état système depuis snapshot pour continuité ou tests

26. get_data_lineage.txt (HybridBaccaratPredictor.get_data_lineage - Lignage données)
    - Lignes 8068-8105 dans hbp.py (38 lignes)
    - FONCTION : Trace lignage et provenance des données pour audit et debugging
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **TRACE SÉQUENCE :** Analyse origine séquence courante :
        - Source initiale (fichier, saisie manuelle, API)
        - Transformations appliquées
        - Timestamps modifications
      * **TRACE HISTORIQUE :** Suit provenance données historiques :
        - Fichiers sources chargés
        - Dates chargement et modifications
        - Validations appliquées
      * **TRACE PRÉDICTIONS :** Analyse historique prédictions :
        - Modèles utilisés pour chaque prédiction
        - Paramètres et configurations
        - Résultats et métriques
      * **TRACE MODÈLES :** Suit évolution modèles :
        - Données entraînement utilisées
        - Hyperparamètres et optimisations
        - Versions et mises à jour
      * **CONSTRUCTION GRAPHE :** Crée graphe dépendances :
        - Nœuds pour chaque source/transformation
        - Arêtes pour relations causales
        - Métadonnées temporelles
      * **ANALYSE IMPACT :** Évalue impact modifications :
        - Propagation changements
        - Composants affectés
        - Risques identifiés
      * **GESTION ERREURS :** Capture exceptions avec lignage partiel
    - RETOUR : Dict[str, Any] - Graphe lignage avec métadonnées et analyse impact
    - UTILITÉ : Traçabilité complète données pour audit, debugging et gouvernance

================================================================================
SECTION 4 : INTERFACE UTILISATEUR
================================================================================

1. create_gui.txt (HybridBaccaratPredictor.create_gui - Création interface graphique)
   - Lignes 9212-9938 dans hbp.py (727 lignes)
   - FONCTION : Crée interface graphique Tkinter complète avec tous composants et fonctionnalités
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION FENÊTRE :** Crée fenêtre principale avec `self.root = tk.Tk()` et configure :
       - Titre : "Hybrid Baccarat Predictor v2.0"
       - Taille : 1400x900 pixels
       - Position centrée sur écran
       - Icône et style personnalisés
     * **CONFIGURATION STYLE :** Définit thème visuel :
       - Couleurs cohérentes (bleu/blanc/gris)
       - Polices standardisées (Arial, tailles variées)
       - Espacement et padding uniformes
       - Style boutons et widgets
     * **FRAME PRINCIPAL :** Structure layout avec `main_frame = ttk.Frame(self.root)` :
       - Grid layout 3 colonnes : contrôles, visualisation, statistiques
       - Redimensionnement adaptatif
       - Séparateurs visuels
     * **PANNEAU CONTRÔLES :** Crée zone contrôles gauche :
       - Boutons actions principales (Prédire, Ajouter résultat, Reset)
       - Champs saisie (résultat manuel, paramètres)
       - Indicateurs état (modèles chargés, données disponibles)
       - Configuration temps réel
     * **PANNEAU VISUALISATION :** Zone centrale avec canvas :
       - Canvas principal 800x600 pour graphiques
       - Onglets multiples (Séquence, Performance, Confiance)
       - Zoom et navigation
       - Export graphiques
     * **PANNEAU STATISTIQUES :** Zone droite informations :
       - Métriques temps réel
       - Historique récent
       - Alertes et notifications
       - Logs système
     * **BARRE MENU :** Menu principal avec `menubar = tk.Menu(self.root)` :
       - Fichier : Charger/Sauvegarder données
       - Modèles : Entraîner/Configurer
       - Outils : Export, Import, Backup
       - Aide : Documentation, À propos
     * **BARRE STATUT :** Statut bas fenêtre :
       - Indicateurs connexion
       - Progression opérations
       - Messages utilisateur
       - Horloge temps réel
     * **BINDINGS ÉVÉNEMENTS :** Configure interactions :
       - Raccourcis clavier (Ctrl+S, Ctrl+O, etc.)
       - Clics souris et survol
       - Redimensionnement fenêtre
       - Fermeture application
     * **INITIALISATION COMPOSANTS :** Lance composants :
       - Threads mise à jour temps réel
       - Timers actualisation
       - Gestionnaires événements
       - Validation formulaires
     * **GESTION ERREURS :** Capture exceptions avec interface dégradée
   - RETOUR : None - Crée directement l'interface
   - UTILITÉ : Interface utilisateur complète pour interaction intuitive avec système

2. run_gui.txt (HybridBaccaratPredictor.run_gui - Lancement interface graphique)
   - Lignes 9940-9970 dans hbp.py (31 lignes)
   - FONCTION : Lance boucle principale interface graphique avec gestion erreurs et nettoyage
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INTERFACE :** Teste `if not hasattr(self, 'root') or self.root is None:` avec création interface si nécessaire
     * **CONFIGURATION FINALE :** Applique paramètres dernière minute et optimisations
     * **LANCEMENT BOUCLE :** Démarre `self.root.mainloop()` avec gestion exceptions
     * **NETTOYAGE RESSOURCES :** Libère ressources à la fermeture
     * **GESTION ERREURS :** Capture exceptions avec interface d'erreur utilisateur
   - RETOUR : None - Bloque jusqu'à fermeture interface
   - UTILITÉ : Point d'entrée principal pour utilisation graphique

3. _on_predict_button.txt (HybridBaccaratPredictor._on_predict_button - Gestionnaire bouton prédiction)
   - Lignes 9972-10002 dans hbp.py (31 lignes)
   - FONCTION : Gestionnaire événement clic bouton prédiction avec validation et affichage résultats
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ÉTAT :** Vérifie `if len(self.sequence) < 3:` avec message utilisateur si données insuffisantes
     * **DÉSACTIVATION BOUTON :** Applique `self.predict_button.config(state='disabled')` pour éviter clics multiples
     * **GÉNÉRATION PRÉDICTION :** Appelle `prediction = self.predict()` pour obtenir prédiction système
     * **MISE À JOUR AFFICHAGE :** Met à jour labels GUI avec résultats via `self._update_prediction_display(prediction)`
     * **RÉACTIVATION BOUTON :** Restaure `self.predict_button.config(state='normal')` après traitement
     * **GESTION ERREURS :** Capture exceptions avec message erreur utilisateur
   - RETOUR : None - Met à jour interface directement
   - UTILITÉ : Interface utilisateur pour déclenchement prédictions avec feedback visuel

4. _on_add_outcome_button.txt (HybridBaccaratPredictor._on_add_outcome_button - Gestionnaire ajout résultat)
   - Lignes 10004-10034 dans hbp.py (31 lignes)
   - FONCTION : Gestionnaire ajout résultat manuel avec validation et mise à jour interface
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION SAISIE :** Utilise `outcome = self.outcome_entry.get().strip().lower()` pour extraction valeur
     * **VALIDATION SAISIE :** Vérifie `if outcome not in ['player', 'banker']:` avec message erreur si invalide
     * **AJOUT RÉSULTAT :** Appelle `self.add_outcome(outcome)` pour mise à jour séquence
     * **NETTOYAGE CHAMP :** Vide `self.outcome_entry.delete(0, tk.END)` pour nouvelle saisie
     * **MISE À JOUR AFFICHAGE :** Actualise graphiques via `self._update_gui()` pour refléter changements
     * **GESTION ERREURS :** Capture exceptions avec message utilisateur
   - RETOUR : None - Met à jour données et interface
   - UTILITÉ : Saisie manuelle résultats avec validation et feedback immédiat

5. _on_reset_button.txt (HybridBaccaratPredictor._on_reset_button - Gestionnaire reset)
   - Lignes 10036-10056 dans hbp.py (21 lignes)
   - FONCTION : Gestionnaire reset système avec confirmation utilisateur
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIRMATION :** Affiche `messagebox.askyesno("Confirmation", "Effacer toutes les données ?")` pour validation
     * **RESET DONNÉES :** Si confirmé, appelle `self.clear_sequence()` pour nettoyage complet
     * **MISE À JOUR INTERFACE :** Actualise `self._update_gui()` pour refléter état vide
     * **MESSAGE SUCCÈS :** Affiche notification reset effectué
   - RETOUR : None - Remet système à zéro
   - UTILITÉ : Reset sécurisé avec confirmation pour nouvelle session

6. _update_gui.txt (HybridBaccaratPredictor._update_gui - Mise à jour interface)
   - Lignes 10058-10088 dans hbp.py (31 lignes)
   - FONCTION : Met à jour tous éléments interface selon état système actuel
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR GRAPHIQUES :** Appelle `self._draw_sequence_chart()`, `self._draw_performance_chart()`, `self._draw_confidence_chart()` pour actualisation
     * **MISE À JOUR STATISTIQUES :** Actualise labels métriques avec données récentes
     * **MISE À JOUR STATUT :** Met à jour barre statut via `self._update_status_bar()`
     * **RAFRAÎCHISSEMENT :** Force `self.root.update_idletasks()` pour affichage immédiat
   - RETOUR : None - Actualise interface complète
   - UTILITÉ : Synchronisation interface avec état système

7. _draw_sequence_chart.txt (HybridBaccaratPredictor._draw_sequence_chart - Graphique séquence)
   - Lignes 10090-10150 dans hbp.py (61 lignes)
   - FONCTION : Dessine graphique séquence avec couleurs et patterns visuels
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NETTOYAGE CANVAS :** Vide `self.sequence_canvas.delete("all")` pour nouveau dessin
     * **VALIDATION DONNÉES :** Vérifie `if not self.sequence:` avec message si vide
     * **CALCUL DIMENSIONS :** Détermine taille points selon `canvas_width` et `len(self.sequence)`
     * **DESSIN POINTS :** Itère séquence avec couleurs : bleu pour Player, rouge pour Banker
     * **AJOUT LÉGENDE :** Dessine légende avec codes couleurs
     * **GESTION ERREURS :** Capture exceptions avec canvas vide
   - RETOUR : None - Dessine directement sur canvas
   - UTILITÉ : Visualisation séquence pour analyse patterns

8. _draw_performance_chart.txt (HybridBaccaratPredictor._draw_performance_chart - Graphique performance)
   - Lignes 10152-10212 dans hbp.py (61 lignes)
   - FONCTION : Dessine graphique performance des méthodes avec barres colorées
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NETTOYAGE CANVAS :** Vide canvas performance pour nouveau dessin
     * **COLLECTE DONNÉES :** Récupère précision chaque méthode via `self.get_method_accuracy()`
     * **CALCUL BARRES :** Détermine hauteur barres selon précision relative
     * **DESSIN BARRES :** Crée rectangles colorés avec hauteurs proportionnelles
     * **AJOUT LABELS :** Affiche noms méthodes et valeurs précision
     * **GESTION ERREURS :** Capture exceptions avec graphique vide
   - RETOUR : None - Dessine graphique performance
   - UTILITÉ : Comparaison visuelle performance méthodes

9. _draw_confidence_chart.txt (HybridBaccaratPredictor._draw_confidence_chart - Graphique confiance)
   - Lignes 10214-10274 dans hbp.py (61 lignes)
   - FONCTION : Dessine évolution confiance sur historique récent avec courbe lissée
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NETTOYAGE CANVAS :** Vide canvas confiance
     * **EXTRACTION DONNÉES :** Collecte confiances depuis `self.prediction_history`
     * **CALCUL POINTS :** Convertit confiances en coordonnées canvas
     * **DESSIN COURBE :** Trace ligne continue entre points confiance
     * **AJOUT SEUILS :** Dessine lignes horizontales pour seuils critiques
     * **GESTION ERREURS :** Capture exceptions avec courbe vide
   - RETOUR : None - Dessine évolution confiance
   - UTILITÉ : Monitoring tendance confiance système

10. _get_color_for_intensity.txt (HybridBaccaratPredictor._get_color_for_intensity - Couleur selon intensité)
    - Lignes 10276-10296 dans hbp.py (21 lignes)
    - FONCTION : Calcule couleur selon intensité valeur pour visualisations graduées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * intensity (float) - Intensité entre 0.0 et 1.0
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION INTENSITÉ :** Borne `intensity = max(0.0, min(1.0, intensity))` pour [0,1]
      * **CALCUL RGB :** Utilise interpolation linéaire pour gradient rouge-vert
      * **CONVERSION HEX :** Convertit RGB en format hexadécimal pour Tkinter
    - RETOUR : str - Code couleur hexadécimal
    - UTILITÉ : Visualisation graduée selon valeurs métriques

11. _on_closing.txt (HybridBaccaratPredictor._on_closing - Gestionnaire fermeture)
    - Lignes 10298-10318 dans hbp.py (21 lignes)
    - FONCTION : Gestionnaire fermeture application avec sauvegarde et nettoyage
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIRMATION :** Affiche dialogue confirmation fermeture si données non sauvées
      * **SAUVEGARDE AUTO :** Appelle `self._save_gui_state()` pour persistance état interface
      * **NETTOYAGE RESSOURCES :** Libère threads et connexions actives
      * **FERMETURE :** Détruit fenêtre avec `self.root.destroy()`
    - RETOUR : None - Ferme application proprement
    - UTILITÉ : Fermeture sécurisée avec sauvegarde automatique

12. _show_about.txt (HybridBaccaratPredictor._show_about - Dialogue à propos)
    - Lignes 10320-10340 dans hbp.py (21 lignes)
    - FONCTION : Affiche dialogue informations application
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION DIALOGUE :** Utilise `messagebox.showinfo()` avec informations système
      * **CONTENU :** Affiche version, auteur, description fonctionnalités
    - RETOUR : None - Affiche dialogue informatif
    - UTILITÉ : Information utilisateur sur application

13. _show_help.txt (HybridBaccaratPredictor._show_help - Aide utilisateur)
    - Lignes 10342-10372 dans hbp.py (31 lignes)
    - FONCTION : Affiche aide contextuelle et documentation utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **FENÊTRE AIDE :** Crée fenêtre dédiée avec `tk.Toplevel()`
      * **CONTENU AIDE :** Affiche instructions utilisation, raccourcis clavier
      * **NAVIGATION :** Permet navigation entre sections aide
    - RETOUR : None - Affiche fenêtre aide
    - UTILITÉ : Support utilisateur intégré

14. _export_chart.txt (HybridBaccaratPredictor._export_chart - Export graphique)
    - Lignes 10374-10404 dans hbp.py (31 lignes)
    - FONCTION : Exporte graphiques vers fichiers image avec options format
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * chart_type (str) - Type graphique à exporter
    - FONCTIONNEMENT DÉTAILLÉ :
      * **SÉLECTION FICHIER :** Dialogue `filedialog.asksaveasfilename()` pour destination
      * **CAPTURE CANVAS :** Convertit canvas en image via PIL
      * **SAUVEGARDE :** Écrit fichier format choisi (PNG, JPG, PDF)
    - RETOUR : bool - True si export réussi
    - UTILITÉ : Export visualisations pour rapports

15. _import_data_gui.txt (HybridBaccaratPredictor._import_data_gui - Import données GUI)
    - Lignes 10406-10436 dans hbp.py (31 lignes)
    - FONCTION : Interface graphique import données avec sélection fichier
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **SÉLECTION FICHIER :** Dialogue `filedialog.askopenfilename()` avec filtres
      * **VALIDATION FORMAT :** Vérifie extension et format fichier
      * **IMPORT DONNÉES :** Appelle `self.import_sequence_data()` avec chemin
      * **MISE À JOUR GUI :** Actualise interface après import
    - RETOUR : None - Importe et actualise interface
    - UTILITÉ : Import convivial données externes

16. _export_data_gui.txt (HybridBaccaratPredictor._export_data_gui - Export données GUI)
    - Lignes 10438-10468 dans hbp.py (31 lignes)
    - FONCTION : Interface graphique export données avec options format
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **SÉLECTION DESTINATION :** Dialogue sauvegarde avec formats supportés
      * **OPTIONS EXPORT :** Checkboxes pour inclure features, métadonnées
      * **EXPORT DONNÉES :** Appelle `self.export_sequence_data()` avec options
      * **CONFIRMATION :** Message succès ou erreur export
    - RETOUR : None - Exporte données selon choix utilisateur
    - UTILITÉ : Export flexible données système

17. _configure_models_gui.txt (HybridBaccaratPredictor._configure_models_gui - Configuration modèles GUI)
    - Lignes 10470-10530 dans hbp.py (61 lignes)
    - FONCTION : Interface configuration modèles avec paramètres ajustables
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **FENÊTRE CONFIG :** Crée `tk.Toplevel()` pour paramètres modèles
      * **ONGLETS MODÈLES :** Notebook avec onglets Markov, LGBM, LSTM
      * **WIDGETS PARAMÈTRES :** Sliders, entries pour hyperparamètres
      * **VALIDATION :** Contrôle cohérence valeurs saisies
      * **APPLICATION :** Met à jour configuration système
    - RETOUR : None - Configure modèles via interface
    - UTILITÉ : Configuration intuitive hyperparamètres

18. _show_settings.txt (HybridBaccaratPredictor._show_settings - Paramètres système)
    - Lignes 10532-10592 dans hbp.py (61 lignes)
    - FONCTION : Interface paramètres généraux système avec sauvegarde
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **FENÊTRE PARAMÈTRES :** Interface complète configuration système
      * **SECTIONS CONFIG :** Groupes logiques (Affichage, Performance, Logging)
      * **WIDGETS APPROPRIÉS :** Checkboxes, comboboxes, sliders selon type
      * **VALIDATION TEMPS RÉEL :** Contrôle valeurs pendant saisie
      * **SAUVEGARDE CONFIG :** Persiste changements dans fichier
    - RETOUR : None - Configure système via GUI
    - UTILITÉ : Personnalisation complète système

19. _update_status_bar.txt (HybridBaccaratPredictor._update_status_bar - MAJ barre statut)
    - Lignes 10594-10614 dans hbp.py (21 lignes)
    - FONCTION : Met à jour barre statut avec informations système actuelles
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * message (str, optionnel) - Message spécifique à afficher
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MESSAGE DÉFAUT :** Si pas de message, affiche état système
      * **INFORMATIONS SYSTÈME :** Nombre séquences, modèles chargés, performance
      * **MISE À JOUR LABEL :** Actualise `self.status_label.config(text=status_text)`
    - RETOUR : None - Met à jour affichage statut
    - UTILITÉ : Information continue état système

20. _show_notification.txt (HybridBaccaratPredictor._show_notification - Notification utilisateur)
    - Lignes 10616-10646 dans hbp.py (31 lignes)
    - FONCTION : Affiche notifications temporaires avec niveaux de priorité
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * message (str) - Message à afficher
      * level (str, optionnel) - Niveau ('info', 'warning', 'error')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION POPUP :** Fenêtre temporaire avec message
      * **STYLE SELON NIVEAU :** Couleurs et icônes selon priorité
      * **TIMER AUTO :** Fermeture automatique après délai
      * **QUEUE NOTIFICATIONS :** Gestion file si multiples messages
    - RETOUR : None - Affiche notification temporaire
    - UTILITÉ : Feedback utilisateur non-bloquant

21. _validate_input.txt (HybridBaccaratPredictor._validate_input - Validation saisie)
    - Lignes 10648-10668 dans hbp.py (21 lignes)
    - FONCTION : Valide saisies utilisateur avec règles métier
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * input_value (str) - Valeur à valider
      * input_type (str) - Type validation ('outcome', 'number', 'config')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SELON TYPE :** Applique règles spécifiques selon `input_type`
      * **MESSAGES ERREUR :** Retourne messages explicites si validation échoue
      * **NORMALISATION :** Standardise format valeurs valides
    - RETOUR : Tuple[bool, str] - (Valide, Message erreur si applicable)
    - UTILITÉ : Validation centralisée saisies utilisateur

22. _handle_gui_error.txt (HybridBaccaratPredictor._handle_gui_error - Gestion erreurs GUI)
    - Lignes 10670-10700 dans hbp.py (31 lignes)
    - FONCTION : Gestionnaire centralisé erreurs interface avec logging et notification
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * error (Exception) - Exception à traiter
      * context (str) - Contexte erreur pour debugging
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LOGGING ERREUR :** Enregistre détails exception avec contexte
      * **NOTIFICATION UTILISATEUR :** Affiche message erreur convivial
      * **RÉCUPÉRATION :** Tente récupération automatique si possible
    - RETOUR : None - Traite erreur avec feedback utilisateur
    - UTILITÉ : Gestion robuste erreurs interface

23. _create_tooltip.txt (HybridBaccaratPredictor._create_tooltip - Création tooltip)
    - Lignes 10702-10722 dans hbp.py (21 lignes)
    - FONCTION : Crée tooltips informatifs pour widgets interface
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * widget - Widget Tkinter pour tooltip
      * text (str) - Texte tooltip à afficher
    - FONCTIONNEMENT DÉTAILLÉ :
      * **BINDING ÉVÉNEMENTS :** Lie survol souris pour affichage/masquage
      * **POSITIONNEMENT :** Calcule position optimale tooltip
      * **STYLE :** Applique style cohérent tooltips
    - RETOUR : None - Ajoute tooltip au widget
    - UTILITÉ : Aide contextuelle interface utilisateur

24. _update_real_time.txt (HybridBaccaratPredictor._update_real_time - MAJ temps réel)
    - Lignes 10724-10754 dans hbp.py (31 lignes)
    - FONCTION : Met à jour interface en temps réel via thread dédié
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **THREAD SÉPARÉ :** Évite blocage interface principale
      * **MISE À JOUR PÉRIODIQUE :** Actualise métriques et graphiques
      * **GESTION RESSOURCES :** Contrôle charge CPU et mémoire
    - RETOUR : None - Met à jour interface en continu
    - UTILITÉ : Interface réactive avec données temps réel

25. _save_gui_state.txt (HybridBaccaratPredictor._save_gui_state - Sauvegarde état GUI)
    - Lignes 10756-10776 dans hbp.py (21 lignes)
    - FONCTION : Sauvegarde état interface pour restauration session
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **COLLECTE ÉTAT :** Rassemble positions, tailles, préférences
      * **SÉRIALISATION :** Convertit en format persistant
      * **SAUVEGARDE FICHIER :** Écrit fichier configuration GUI
    - RETOUR : bool - True si sauvegarde réussie
    - UTILITÉ : Persistance préférences utilisateur

26. _load_gui_state.txt (HybridBaccaratPredictor._load_gui_state - Chargement état GUI)
    - Lignes 10778-10798 dans hbp.py (21 lignes)
    - FONCTION : Restaure état interface depuis sauvegarde précédente
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CHARGEMENT FICHIER :** Lit configuration GUI sauvée
      * **VALIDATION :** Vérifie cohérence paramètres chargés
      * **APPLICATION :** Restaure positions, tailles, préférences
    - RETOUR : bool - True si chargement réussi
    - UTILITÉ : Continuité expérience utilisateur

27. _customize_appearance.txt (HybridBaccaratPredictor._customize_appearance - Personnalisation apparence)
    - Lignes 10800-10830 dans hbp.py (31 lignes)
    - FONCTION : Permet personnalisation apparence interface (thèmes, couleurs)
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * theme (str, optionnel) - Thème à appliquer
    - FONCTIONNEMENT DÉTAILLÉ :
      * **THÈMES PRÉDÉFINIS :** Sélection parmi thèmes disponibles
      * **PERSONNALISATION :** Modification couleurs, polices individuelles
      * **APPLICATION :** Met à jour tous widgets interface
    - RETOUR : None - Applique personnalisation interface
    - UTILITÉ : Personnalisation visuelle selon préférences

28. _setup_logging_gui.txt (HybridBaccaratPredictor._setup_logging_gui - Configuration logging GUI)
    - Lignes 10832-10852 dans hbp.py (21 lignes)
    - FONCTION : Configure affichage logs dans interface avec filtrage
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **WIDGET LOGS :** Crée zone affichage logs dans interface
      * **FILTRAGE :** Permet filtrage par niveau et catégorie
      * **MISE À JOUR :** Actualise affichage en temps réel
    - RETOUR : None - Configure affichage logs GUI
    - UTILITÉ : Monitoring système intégré interface

29. _create_progress_dialog.txt (HybridBaccaratPredictor._create_progress_dialog - Dialogue progression)
    - Lignes 10854-10884 dans hbp.py (31 lignes)
    - FONCTION : Crée dialogues progression pour opérations longues
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * title (str) - Titre dialogue progression
      * max_value (int) - Valeur maximale progression
    - FONCTIONNEMENT DÉTAILLÉ :
      * **FENÊTRE MODALE :** Crée dialogue bloquant avec barre progression
      * **MISE À JOUR :** Méthodes pour actualiser progression
      * **ANNULATION :** Bouton annulation pour opérations interruptibles
    - RETOUR : ProgressDialog - Objet dialogue progression
    - UTILITÉ : Feedback visuel opérations longues

================================================================================
SECTION 5 : OPTIMISATION ENTRAINEMENT
================================================================================

1. _train_models.txt (HybridBaccaratPredictor._train_models - Entraînement modèles)
   - Lignes 2060-2580 dans hbp.py (521 lignes)
   - FONCTION : Entraîne tous les modèles (LGBM, LSTM, Markov) avec optimisation hyperparamètres et validation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_lgbm, y_lgbm, X_lstm, sample_weights, train_indices, val_indices - Données préparées
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Vérifie `if X_lgbm is None or y_lgbm is None:` avec logging erreur et retour False
     * **SPLIT DONNÉES :** Divise données avec `X_train, X_val = X_lgbm[train_indices], X_lgbm[val_indices]` pour entraînement/validation
     * **ENTRAÎNEMENT LGBM :** Appelle `lgbm_success = self._train_lgbm(X_train, y_train, X_val, y_val, sample_weights[train_indices])`
     * **ENTRAÎNEMENT LSTM :** Si données suffisantes, lance `lstm_success = self._train_lstm(X_lstm_train, y_train, X_lstm_val, y_val)`
     * **ENTRAÎNEMENT MARKOV :** Met à jour `self.markov.train(data_source)` avec nouvelles données
     * **VALIDATION CROISÉE :** Évalue performance avec `cv_scores = self._cross_validate_models(X_lgbm, y_lgbm)`
     * **CALIBRATION PROBABILITÉS :** Applique `self._calibrate_models(X_val, y_val)` pour améliorer confiance
     * **SAUVEGARDE MODÈLES :** Persiste modèles via `self.save_models()` pour réutilisation
     * **MÉTRIQUES FINALES :** Calcule métriques complètes avec `final_metrics = self._calculate_training_metrics()`
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Entraînement terminé - LGBM: {lgbm_success}, LSTM: {lstm_success}")` pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec sauvegarde état partiel
   - RETOUR : Dict[str, Any] - Métriques entraînement et validation avec succès par modèle
   - UTILITÉ : Orchestration complète entraînement avec optimisation automatique et validation robuste

2. _train_lgbm.txt (HybridBaccaratPredictor._train_lgbm - Entraînement LGBM)
   - Lignes 2582-2742 dans hbp.py (161 lignes)
   - FONCTION : Entraîne modèle LightGBM avec optimisation hyperparamètres et validation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_train, y_train, X_val, y_val - Données entraînement et validation
     * sample_weights - Poids échantillons pour entraînement pondéré
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Vérifie `if X_train is None or len(X_train) == 0:` avec retour False si insuffisant
     * **OPTIMISATION HYPERPARAMÈTRES :** Appelle `best_params = self._optimize_lgbm_hyperparameters(X_train, y_train, X_val, y_val)` pour recherche optimale
     * **CONFIGURATION MODÈLE :** Crée `lgb_train = lgb.Dataset(X_train, label=y_train, weight=sample_weights)` et `lgb_val = lgb.Dataset(X_val, label=y_val, reference=lgb_train)`
     * **ENTRAÎNEMENT :** Lance `self.lgbm_base = lgb.train(best_params, lgb_train, valid_sets=[lgb_val], callbacks=[lgb.early_stopping(50), lgb.log_evaluation(100)])`
     * **VALIDATION PERFORMANCE :** Évalue avec `val_predictions = self.lgbm_base.predict(X_val)` puis calcule métriques
     * **SAUVEGARDE MÉTRIQUES :** Stocke performance dans `self.lgbm_metrics` pour monitoring
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour False
   - RETOUR : bool - True si entraînement réussi, False sinon
   - UTILITÉ : Entraînement spécialisé LGBM avec optimisation automatique

3. _train_lstm.txt (HybridBaccaratPredictor._train_lstm - Entraînement LSTM)
   - Lignes 2744-2904 dans hbp.py (161 lignes)
   - FONCTION : Entraîne réseau LSTM avec architecture optimisée et callbacks
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_train, y_train, X_val, y_val - Données séquentielles entraînement/validation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DIMENSIONS :** Vérifie `if X_train.ndim != 3:` avec reshape si nécessaire pour format LSTM
     * **CRÉATION MODÈLE :** Appelle `self.lstm_model = self._create_lstm_model(input_shape=X_train.shape[1:])` pour architecture
     * **CONFIGURATION CALLBACKS :** Setup `callbacks = [self._early_stopping_callback(), self._reduce_lr_callback(), self._model_checkpoint_callback()]`
     * **ENTRAÎNEMENT :** Lance `history = self.lstm_model.fit(X_train, y_train, validation_data=(X_val, y_val), epochs=100, batch_size=32, callbacks=callbacks, verbose=1)`
     * **ÉVALUATION :** Teste performance avec `val_loss, val_accuracy = self.lstm_model.evaluate(X_val, y_val, verbose=0)`
     * **SAUVEGARDE HISTORIQUE :** Stocke courbes apprentissage dans `self.lstm_history = history.history`
     * **GESTION ERREURS :** Capture exceptions TensorFlow avec fallback
   - RETOUR : bool - True si entraînement réussi, False sinon
   - UTILITÉ : Entraînement réseau neuronal avec callbacks et monitoring

4. _create_lstm_model.txt (HybridBaccaratPredictor._create_lstm_model - Création modèle LSTM)
   - Lignes 2906-2966 dans hbp.py (61 lignes)
   - FONCTION : Crée architecture LSTM optimisée avec couches et régularisation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * input_shape - Forme données entrée (sequence_length, features)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ARCHITECTURE SÉQUENTIELLE :** Crée `model = tf.keras.Sequential()` pour empilement couches
     * **COUCHES LSTM :** Ajoute `model.add(LSTM(64, return_sequences=True, dropout=0.2, recurrent_dropout=0.2))` puis `model.add(LSTM(32, dropout=0.2))`
     * **COUCHES DENSES :** Ajoute `model.add(Dense(16, activation='relu'))` puis `model.add(Dropout(0.3))` et `model.add(Dense(1, activation='sigmoid'))`
     * **COMPILATION :** Configure `model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])`
     * **RÉSUMÉ MODÈLE :** Affiche architecture avec `model.summary()` pour debugging
   - RETOUR : tf.keras.Model - Modèle LSTM compilé prêt entraînement
   - UTILITÉ : Architecture LSTM standardisée avec régularisation

5. _optimize_lgbm_hyperparameters.txt (HybridBaccaratPredictor._optimize_lgbm_hyperparameters - Optimisation hyperparamètres LGBM)
   - Lignes 2968-3128 dans hbp.py (161 lignes)
   - FONCTION : Optimise hyperparamètres LGBM via recherche bayésienne ou grid search
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_train, y_train, X_val, y_val - Données pour optimisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ESPACE RECHERCHE :** Définit `param_space = {'num_leaves': [31, 50, 100], 'learning_rate': [0.01, 0.05, 0.1], 'feature_fraction': [0.8, 0.9, 1.0]}`
     * **VALIDATION CROISÉE :** Utilise `StratifiedKFold(n_splits=3)` pour évaluation robuste
     * **RECHERCHE GRID :** Itère combinaisons avec `for params in ParameterGrid(param_space):`
     * **ÉVALUATION PERFORMANCE :** Calcule `cv_scores = cross_val_score(lgb_model, X_train, y_train, cv=cv, scoring='accuracy')`
     * **SÉLECTION MEILLEURS :** Garde `best_params` avec score maximal
     * **LOGGING OPTIMISATION :** Enregistre progression et résultats
   - RETOUR : Dict[str, Any] - Meilleurs hyperparamètres trouvés
   - UTILITÉ : Optimisation automatique performance LGBM

6. _optimize_lstm_hyperparameters.txt (HybridBaccaratPredictor._optimize_lstm_hyperparameters - Optimisation hyperparamètres LSTM)
   - Lignes 3130-3290 dans hbp.py (161 lignes)
   - FONCTION : Optimise architecture et hyperparamètres LSTM via recherche structurée
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_train, y_train, X_val, y_val - Données pour optimisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ESPACE ARCHITECTURE :** Définit variations `lstm_units = [32, 64, 128]`, `dense_units = [8, 16, 32]`, `dropout_rates = [0.2, 0.3, 0.5]`
     * **RECHERCHE COMBINAISONS :** Teste architectures avec `for lstm_unit in lstm_units: for dense_unit in dense_units:`
     * **ENTRAÎNEMENT RAPIDE :** Utilise `epochs=10` pour évaluation rapide configurations
     * **ÉVALUATION VALIDATION :** Mesure `val_accuracy` pour sélection
     * **SÉLECTION OPTIMALE :** Retient configuration avec meilleure performance validation
   - RETOUR : Dict[str, Any] - Meilleure configuration architecture trouvée
   - UTILITÉ : Optimisation architecture LSTM pour performance maximale

7. _validate_models.txt (HybridBaccaratPredictor._validate_models - Validation modèles)
   - Lignes 3292-3352 dans hbp.py (61 lignes)
   - FONCTION : Valide performance modèles entraînés avec métriques multiples
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_test, y_test - Données test pour validation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LGBM :** Teste `lgbm_predictions = self.lgbm_base.predict(X_test)` avec calcul métriques
     * **VALIDATION LSTM :** Évalue `lstm_predictions = self.lstm_model.predict(X_test)` avec conversion probabilités
     * **MÉTRIQUES COMPARATIVES :** Calcule accuracy, precision, recall, F1-score pour chaque modèle
     * **TESTS STATISTIQUES :** Applique tests significativité différences performance
   - RETOUR : Dict[str, Dict] - Métriques validation par modèle
   - UTILITÉ : Évaluation objective performance modèles entraînés

8. _calibrate_models.txt (HybridBaccaratPredictor._calibrate_models - Calibration modèles)
   - Lignes 3354-3414 dans hbp.py (61 lignes)
   - FONCTION : Calibre probabilités modèles pour améliorer confiance prédictions
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_cal, y_cal - Données calibration
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALIBRATION LGBM :** Utilise `CalibratedClassifierCV` pour calibrer probabilités LGBM
     * **CALIBRATION LSTM :** Applique transformation sigmoid calibrée sur sorties LSTM
     * **VALIDATION CALIBRATION :** Teste qualité calibration avec reliability diagrams
   - RETOUR : bool - True si calibration réussie
   - UTILITÉ : Amélioration fiabilité probabilités prédites

9. save_models.txt (HybridBaccaratPredictor.save_models - Sauvegarde modèles)
   - Lignes 3416-3476 dans hbp.py (61 lignes)
   - FONCTION : Sauvegarde tous modèles entraînés vers fichiers persistants
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * models_dir (str, optionnel) - Répertoire sauvegarde
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SAUVEGARDE LGBM :** Utilise `self.lgbm_base.save_model('lgbm_model.txt')` pour persistance
     * **SAUVEGARDE LSTM :** Applique `self.lstm_model.save('lstm_model.h5')` pour TensorFlow
     * **SAUVEGARDE MARKOV :** Sérialise transitions avec pickle
     * **MÉTADONNÉES :** Sauve configuration et métriques associées
   - RETOUR : bool - True si sauvegarde complète réussie
   - UTILITÉ : Persistance modèles pour réutilisation sessions futures

10. load_models.txt (HybridBaccaratPredictor.load_models - Chargement modèles)
    - Lignes 3478-3538 dans hbp.py (61 lignes)
    - FONCTION : Charge modèles sauvegardés depuis fichiers avec validation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * models_dir (str, optionnel) - Répertoire modèles
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CHARGEMENT LGBM :** Utilise `lgb.Booster(model_file='lgbm_model.txt')` pour restauration
      * **CHARGEMENT LSTM :** Applique `tf.keras.models.load_model('lstm_model.h5')` pour TensorFlow
      * **CHARGEMENT MARKOV :** Désérialise transitions depuis pickle
      * **VALIDATION COHÉRENCE :** Vérifie compatibilité modèles avec configuration courante
    - RETOUR : bool - True si chargement complet réussi
    - UTILITÉ : Restauration modèles pour continuité sessions

11. _setup_callbacks.txt (HybridBaccaratPredictor._setup_callbacks - Configuration callbacks)
    - Lignes 3540-3600 dans hbp.py (61 lignes)
    - FONCTION : Configure callbacks pour entraînement LSTM avec early stopping et monitoring
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EARLY STOPPING :** Crée `EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)`
      * **REDUCE LR :** Configure `ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=5)`
      * **MODEL CHECKPOINT :** Setup `ModelCheckpoint(filepath='best_model.h5', save_best_only=True)`
      * **LOGGING :** Ajoute callback logging personnalisé pour métriques
    - RETOUR : List[Callback] - Liste callbacks configurés
    - UTILITÉ : Optimisation entraînement avec arrêt précoce et sauvegarde

12. _early_stopping_callback.txt (HybridBaccaratPredictor._early_stopping_callback - Callback early stopping)
    - Lignes 3602-3632 dans hbp.py (31 lignes)
    - FONCTION : Crée callback early stopping personnalisé avec critères adaptatifs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION :** Utilise `EarlyStopping(monitor='val_accuracy', patience=15, mode='max')`
      * **CRITÈRES ADAPTATIFS :** Ajuste patience selon taille dataset
      * **RESTAURATION :** Active `restore_best_weights=True` pour meilleur modèle
    - RETOUR : EarlyStopping - Callback configuré
    - UTILITÉ : Prévention overfitting avec arrêt intelligent

13. _cross_validate_models.txt (HybridBaccaratPredictor._cross_validate_models - Validation croisée)
    - Lignes 3634-3704 dans hbp.py (71 lignes)
    - FONCTION : Effectue validation croisée sur modèles avec stratification temporelle
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_data, y_data - Données pour validation
      * cv_folds (int, optionnel) - Nombre de folds (défaut: 5)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **STRATIFICATION :** Utilise `TimeSeriesSplit(n_splits=cv_folds)` pour données temporelles
      * **VALIDATION PAR FOLD :** Pour chaque fold, entraîne et évalue modèles
      * **MÉTRIQUES :** Collecte accuracy, precision, recall, f1 par fold
      * **STATISTIQUES :** Calcule moyenne et écart-type des métriques
      * **STABILITÉ :** Évalue variance performance entre folds
    - RETOUR : Dict[str, Dict] - Métriques CV par modèle avec statistiques
    - UTILITÉ : Évaluation robuste performance avec estimation variance

[Méthodes 14-32 d'OptimisationEntrainement : _reduce_lr_callback, _model_checkpoint_callback, _tensorboard_callback, _custom_metric_callback, _grid_search_lgbm, _random_search_lstm, _bayesian_optimization, _ensemble_training, _transfer_learning, _incremental_learning, _model_compression, _quantization, _pruning, _knowledge_distillation, _federated_learning, _auto_ml_pipeline, _hyperparameter_tuning, _model_selection, _performance_monitoring]

================================================================================
SECTION 6 : RESEAUX NEURONAUX
================================================================================

1. predict_lstm.txt (HybridBaccaratPredictor.predict_lstm - Prédiction LSTM)
   - Lignes 5738-5808 dans hbp.py (71 lignes)
   - FONCTION : Génère prédiction via réseau LSTM avec gestion erreurs et normalisation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence pour prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** Vérifie `if self.lstm_model is None:` avec logging warning et retour prédiction par défaut
     * **PRÉPARATION FEATURES :** Appelle `lstm_features = self.create_lstm_features(sequence)` pour génération features temporelles
     * **VALIDATION FEATURES :** Teste `if lstm_features is None:` avec fallback vers prédiction neutre
     * **RESHAPE DONNÉES :** Assure format correct avec `lstm_features = lstm_features.reshape(1, -1, lstm_features.shape[-1])` pour batch size 1
     * **PRÉDICTION MODÈLE :** Utilise `raw_prediction = self.lstm_model.predict(lstm_features, verbose=0)[0][0]` pour obtenir probabilité brute
     * **NORMALISATION :** Applique `player_prob = float(np.clip(raw_prediction, 0.0, 1.0))` pour borner probabilité
     * **CALCUL BANKER :** Détermine `banker_prob = 1.0 - player_prob` pour probabilité complémentaire
     * **CALCUL CONFIANCE :** Évalue confiance avec `confidence = abs(player_prob - 0.5) * 2.0` pour mesure certitude
     * **ASSEMBLAGE RÉSULTAT :** Structure `result = {'player': player_prob, 'banker': banker_prob, 'confidence': confidence, 'method': 'lstm'}`
     * **LOGGING DEBUG :** Enregistre `logger.debug(f"LSTM prediction: {result}")` pour traçabilité
     * **GESTION ERREURS :** Capture exceptions TensorFlow avec fallback prédiction par défaut `{'player': 0.5, 'banker': 0.5, 'confidence': 0.0, 'method': 'lstm'}`
   - RETOUR : Dict[str, float] - Probabilités Player/Banker avec confiance et métadonnées
   - UTILITÉ : Prédiction réseau neuronal robuste avec fallbacks et normalisation

2. _lstm_predict_proba.txt (HybridBaccaratPredictor._lstm_predict_proba - Probabilités LSTM)
   - Lignes 5810-5840 dans hbp.py (31 lignes)
   - FONCTION : Calcule probabilités LSTM avec gestion batch et normalisation avancée
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_lstm - Features LSTM formatées pour prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE :** Vérifie `if X_lstm is None or len(X_lstm) == 0:` avec retour probabilités par défaut
     * **VALIDATION MODÈLE :** Teste `if self.lstm_model is None:` avec logging warning et retour `np.array([[0.5, 0.5]])`
     * **RESHAPE DONNÉES :** Assure format correct avec `if X_lstm.ndim == 2: X_lstm = X_lstm.reshape(1, X_lstm.shape[0], X_lstm.shape[1])`
     * **PRÉDICTION BATCH :** Utilise `probabilities = self.lstm_model.predict(X_lstm, verbose=0)` pour traitement efficace
     * **NORMALISATION :** Applique `probabilities = np.clip(probabilities, 0.0, 1.0)` pour borner probabilités
     * **SOFTMAX OPTIONNEL :** Si nécessaire, applique `probabilities = softmax(probabilities, axis=1)` pour normalisation
     * **VALIDATION SORTIE :** Vérifie `if np.any(np.isnan(probabilities)) or np.any(np.isinf(probabilities)):` avec correction
     * **GESTION ERREURS :** Capture exceptions TensorFlow avec fallback `np.array([[0.5, 0.5]])`
   - RETOUR : np.ndarray - Probabilités normalisées shape (batch_size, 2) pour chaque échantillon
   - UTILITÉ : Interface bas niveau pour prédictions LSTM batch avec robustesse maximale

3. _prepare_lstm_input.txt (HybridBaccaratPredictor._prepare_lstm_input - Préparation entrée LSTM)
   - Lignes 5842-5872 dans hbp.py (31 lignes)
   - FONCTION : Prépare données entrée pour modèle LSTM avec formatage et validation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence brute à formater
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence or not isinstance(sequence, list):` avec retour None si invalide
     * **GÉNÉRATION FEATURES :** Appelle `lstm_features = self.create_lstm_features(sequence)` pour extraction features temporelles
     * **VALIDATION FEATURES :** Teste `if lstm_features is None:` avec logging warning et retour None
     * **VALIDATION FORMAT :** Vérifie dimensions avec `if lstm_features.ndim != 3:` et tente reshape si nécessaire
     * **VALIDATION DIMENSIONS :** Contrôle `expected_shape = (1, self.config.lstm_sequence_length, self.config.lstm_input_size)` pour cohérence
     * **RESHAPE BATCH :** Formate pour prédiction avec `input_data = lstm_features.reshape(1, lstm_features.shape[-2], lstm_features.shape[-1])` si nécessaire
     * **VALIDATION FINALE :** Vérifie `if input_data.shape[1:] != expected_shape[1:]:` avec logging erreur et retour None
     * **NORMALISATION :** Applique `input_data = np.clip(input_data, -10.0, 10.0)` pour éviter valeurs extrêmes
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour None
   - RETOUR : Optional[np.ndarray] - Données formatées shape (1, sequence_length, input_size) ou None si échec
   - UTILITÉ : Préparation standardisée et robuste entrées LSTM avec validation complète

================================================================================
SECTION 7 : UTILITAIRES FONCTIONS
================================================================================

1. predict.txt (HybridBaccaratPredictor.predict - Prédiction principale)
   - Lignes 4830-5736 dans hbp.py (907 lignes)
   - FONCTION : Point d'entrée principal pour prédictions hybrides avec ensemble de modèles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_round_num (int, optionnel) - Numéro manche courante
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Vérifie `if len(self.sequence) < 3:` avec retour prédiction par défaut si données insuffisantes
     * **PRÉDICTION MARKOV :** Appelle `markov_pred = self.predict_markov(self.sequence)` pour prédiction chaîne Markov
     * **PRÉDICTION LGBM :** Utilise `lgbm_pred = self.predict_lgbm(self.sequence)` pour modèle gradient boosting
     * **PRÉDICTION LSTM :** Lance `lstm_pred = self.predict_lstm(self.sequence)` pour réseau neuronal
     * **COLLECTE PRÉDICTIONS :** Rassemble `predictions = [markov_pred, lgbm_pred, lstm_pred]` avec validation disponibilité
     * **CALCUL POIDS :** Détermine poids adaptatifs avec `weights = self._calculate_ensemble_weights(predictions)` selon performance récente
     * **ENSEMBLE :** Combine prédictions via `combined_pred = self._combine_predictions(predictions, weights)` pour prédiction finale
     * **CALCUL CONFIANCE :** Évalue confiance ensemble avec `confidence = self._calculate_ensemble_confidence(predictions, combined_pred)`
     * **ANALYSE INCERTITUDE :** Calcule incertitudes épistémique/aléatoire pour robustesse
     * **RECOMMANDATION :** Détermine recommandation avec `recommendation = self._make_recommendation(combined_pred, confidence)` selon seuils
     * **LOGGING PRÉDICTION :** Enregistre dans historique via `self._log_prediction(combined_pred, confidence, recommendation, current_round_num)`
     * **FORMATAGE SORTIE :** Structure résultat avec `self._format_prediction_output(combined_pred, confidence, recommendation, predictions)`
     * **GESTION ERREURS :** Capture exceptions avec prédiction de sécurité par défaut
   - RETOUR : Dict[str, Any] - Prédiction complète avec métadonnées, confiance et recommandation
   - UTILITÉ : Interface principale prédiction avec logique ensemble sophistiquée et gestion robuste

2. predict_markov.txt (HybridBaccaratPredictor.predict_markov - Prédiction Markov)
   - Lignes 5874-5944 dans hbp.py (71 lignes)
   - FONCTION : Génère prédiction via chaîne de Markov avec analyse transitions et probabilités
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence pour analyse transitions
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** Vérifie `if not hasattr(self, 'markov') or self.markov is None:` avec retour prédiction par défaut
     * **VALIDATION SÉQUENCE :** Teste `if not sequence or len(sequence) < 2:` avec retour probabilités uniformes
     * **EXTRACTION ÉTAT :** Utilise `current_state = sequence[-1].lower().strip()` pour état actuel
     * **VALIDATION ÉTAT :** Vérifie `if current_state not in ['player', 'banker']:` avec fallback état neutre
     * **CALCUL TRANSITIONS :** Appelle `transition_probs = self.markov.get_transition_probabilities(current_state)` pour probabilités
     * **VALIDATION PROBABILITÉS :** Teste `if not transition_probs or not isinstance(transition_probs, dict):` avec probabilités par défaut
     * **EXTRACTION PROBS :** Récupère `player_prob = transition_probs.get('player', 0.5)` et `banker_prob = transition_probs.get('banker', 0.5)`
     * **NORMALISATION :** Applique `total = player_prob + banker_prob` puis normalise si `total > 0: player_prob /= total; banker_prob /= total`
     * **CALCUL CONFIANCE :** Évalue confiance avec `confidence = abs(player_prob - 0.5) * 2.0` pour mesure certitude
     * **AJUSTEMENT HISTORIQUE :** Si historique suffisant, ajuste confiance selon performance récente Markov
     * **ASSEMBLAGE RÉSULTAT :** Structure `result = {'player': player_prob, 'banker': banker_prob, 'confidence': confidence, 'method': 'markov'}`
     * **GESTION ERREURS :** Capture exceptions avec fallback prédiction neutre
   - RETOUR : Dict[str, float] - Probabilités Player/Banker avec confiance et métadonnées
   - UTILITÉ : Prédiction basée transitions historiques avec analyse probabiliste

3. predict_lgbm.txt (HybridBaccaratPredictor.predict_lgbm - Prédiction LGBM)
   - Lignes 5946-6016 dans hbp.py (71 lignes)
   - FONCTION : Génère prédiction via modèle LightGBM avec features sophistiquées
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence pour extraction features
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** Vérifie `if not hasattr(self, 'lgbm_base') or self.lgbm_base is None:` avec retour prédiction par défaut
     * **GÉNÉRATION FEATURES :** Appelle `lgbm_features, _ = self.create_lgbm_features(sequence)` pour extraction features
     * **VALIDATION FEATURES :** Teste `if lgbm_features is None or len(lgbm_features) == 0:` avec fallback features basiques
     * **EXTRACTION DERNIÈRE :** Utilise `last_features = lgbm_features[-1].reshape(1, -1)` pour dernière observation
     * **PRÉDICTION PROBABILITÉS :** Appelle `probabilities = self.lgbm_base.predict(last_features, num_iteration=self.lgbm_base.best_iteration)`
     * **VALIDATION PRÉDICTION :** Vérifie format et borne `probabilities = np.clip(probabilities[0], 0.0, 1.0)` si array
     * **CALCUL COMPLÉMENTAIRE :** Détermine `player_prob = probabilities` et `banker_prob = 1.0 - probabilities`
     * **CALCUL CONFIANCE :** Évalue avec `confidence = abs(player_prob - 0.5) * 2.0` plus ajustements selon features
     * **BOOST CONFIANCE :** Si features indiquent patterns forts, applique boost confiance
     * **ASSEMBLAGE RÉSULTAT :** Structure résultat avec métadonnées modèle
     * **GESTION ERREURS :** Capture exceptions LightGBM avec prédiction de sécurité
   - RETOUR : Dict[str, float] - Probabilités Player/Banker avec confiance et métadonnées
   - UTILITÉ : Prédiction machine learning avec features avancées et boost confiance

4. _combine_predictions.txt (HybridBaccaratPredictor._combine_predictions - Combinaison prédictions)
   - Lignes 6018-6088 dans hbp.py (71 lignes)
   - FONCTION : Combine prédictions multiples avec poids adaptatifs et validation cohérence
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * predictions (List[Dict]) - Liste prédictions individuelles
     * weights (Dict[str, float]) - Poids par méthode
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉES :** Vérifie `if not predictions or not weights:` avec retour prédiction par défaut
     * **FILTRAGE VALIDES :** Filtre `valid_predictions = [p for p in predictions if p and 'player' in p and 'banker' in p]`
     * **VALIDATION MINIMUM :** Teste `if len(valid_predictions) == 0:` avec retour probabilités uniformes
     * **INITIALISATION :** Crée `combined_player = 0.0`, `combined_banker = 0.0`, `total_weight = 0.0`
     * **COMBINAISON PONDÉRÉE :** Itère prédictions avec `for pred in valid_predictions:` :
       - Récupère `method = pred.get('method', 'unknown')` et `weight = weights.get(method, 0.0)`
       - Accumule `combined_player += pred['player'] * weight` et `combined_banker += pred['banker'] * weight`
       - Somme `total_weight += weight`
     * **NORMALISATION :** Si `total_weight > 0:`, normalise `combined_player /= total_weight` et `combined_banker /= total_weight`
     * **VALIDATION FINALE :** Assure `combined_player + combined_banker ≈ 1.0` avec correction si nécessaire
     * **CALCUL CONFIANCE :** Combine confiances individuelles avec pondération et analyse consensus
     * **MÉTADONNÉES :** Ajoute informations sur méthodes utilisées et poids appliqués
     * **GESTION ERREURS :** Capture exceptions avec combinaison simple moyenne
   - RETOUR : Dict[str, float] - Prédiction combinée avec confiance et métadonnées
   - UTILITÉ : Ensemble intelligent avec pondération adaptative et validation robuste

5. _make_recommendation.txt (HybridBaccaratPredictor._make_recommendation - Génération recommandation)
   - Lignes 6090-6160 dans hbp.py (71 lignes)
   - FONCTION : Génère recommandation WAIT/NON-WAIT basée sur prédiction et confiance
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction (Dict[str, Any]) - Prédiction avec probabilités et confiance
     * confidence (float) - Niveau de confiance de la prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉDICTION :** Vérifie `if not prediction or 'player' not in prediction:` avec retour 'WAIT'
     * **EXTRACTION PROBABILITÉS :** Récupère `player_prob = prediction['player']` et `banker_prob = prediction['banker']`
     * **SEUIL CONFIANCE :** Appelle `confidence_threshold = self.get_confidence_threshold()` pour seuil adaptatif
     * **TEST CONFIANCE :** Vérifie `if confidence < confidence_threshold:` avec retour 'WAIT' si confiance insuffisante
     * **ANALYSE ÉCART :** Calcule `prob_difference = abs(player_prob - banker_prob)` pour mesure certitude
     * **SEUIL PROBABILITÉ :** Teste `if prob_difference < 0.1:` avec retour 'WAIT' si prédiction trop équilibrée
     * **VALIDATION HISTORIQUE :** Analyse performance récente pour ajustement seuils
     * **FACTEURS CONTEXTUELS :** Considère facteurs additionnels :
       - Longueur séquence actuelle
       - Patterns récents détectés
       - Stabilité confiance récente
       - Performance méthode dominante
     * **DÉCISION FINALE :** Si tous critères satisfaits, retourne 'NON-WAIT' avec outcome prédit
     * **LOGGING DÉCISION :** Enregistre logique décision pour traçabilité
     * **GESTION ERREURS :** Capture exceptions avec retour conservateur 'WAIT'
   - RETOUR : str - Recommandation 'WAIT' ou 'NON-WAIT' avec justification
   - UTILITÉ : Décision intelligente avec critères multiples et approche conservatrice

6. _log_prediction.txt (HybridBaccaratPredictor._log_prediction - Logging prédiction)
   - Lignes 6162-6212 dans hbp.py (51 lignes)
   - FONCTION : Enregistre prédiction dans historique avec métadonnées complètes pour apprentissage
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction (Dict[str, Any]) - Prédiction à enregistrer
     * confidence (float) - Confiance de la prédiction
     * recommendation (str) - Recommandation générée
     * round_num (int, optionnel) - Numéro de manche
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉDICTION :** Vérifie `if not prediction or not isinstance(prediction, dict):` avec retour si invalide
     * **CRÉATION ENTRÉE :** Structure `log_entry = {'timestamp': datetime.now().isoformat(), 'prediction': prediction, 'confidence': confidence, 'recommendation': recommendation}`
     * **MÉTADONNÉES :** Ajoute `round_num`, `sequence_length`, `method_weights` actuels
     * **AJOUT HISTORIQUE :** Appelle `self.prediction_history.append(log_entry)` pour stockage
     * **LIMITATION TAILLE :** Si `len(self.prediction_history) > 1000:`, applique `self.prediction_history = self.prediction_history[-1000:]`
     * **GESTION ERREURS :** Capture exceptions avec logging minimal
   - RETOUR : None - Enregistre directement dans historique
   - UTILITÉ : Traçabilité complète prédictions pour analyse et apprentissage

7. get_recommendation.txt (HybridBaccaratPredictor.get_recommendation - Obtention recommandation)
   - Lignes 6214-6244 dans hbp.py (31 lignes)
   - FONCTION : Interface publique pour obtenir recommandation basée sur séquence actuelle
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_round_num (int, optionnel) - Numéro manche courante
   - FONCTIONNEMENT DÉTAILLÉ :
     * **GÉNÉRATION PRÉDICTION :** Appelle `prediction_result = self.predict(current_round_num)` pour prédiction complète
     * **EXTRACTION RECOMMANDATION :** Récupère `recommendation = prediction_result.get('recommendation', 'WAIT')`
     * **VALIDATION :** Vérifie format recommandation et cohérence
     * **GESTION ERREURS :** Retourne 'WAIT' par défaut si erreur
   - RETOUR : str - Recommandation 'WAIT' ou 'NON-WAIT'
   - UTILITÉ : Interface simple pour obtention recommandation

8. _calculate_ensemble_weights.txt (HybridBaccaratPredictor._calculate_ensemble_weights - Calcul poids ensemble)
   - Lignes 6246-6316 dans hbp.py (71 lignes)
   - FONCTION : Calcule poids adaptatifs pour ensemble basés sur performance récente et confiance
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * predictions (List[Dict]) - Prédictions individuelles avec confiances
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Crée `weights = {}` et récupère performance récente chaque méthode
     * **CALCUL PERFORMANCE :** Pour chaque méthode, utilise `accuracy = self.get_method_accuracy(method)` et `recent_accuracy = self.get_recent_accuracy(window_size=10)`
     * **FACTEUR CONFIANCE :** Analyse confiance moyenne récente par méthode
     * **POIDS ADAPTATIFS :** Combine performance et confiance avec `weight = (accuracy * 0.7 + confidence_factor * 0.3)`
     * **NORMALISATION :** Assure somme poids = 1.0
     * **GESTION ERREURS :** Utilise poids uniformes si calcul échoue
   - RETOUR : Dict[str, float] - Poids normalisés par méthode
   - UTILITÉ : Optimisation automatique pondération ensemble

9. _format_prediction_output.txt (HybridBaccaratPredictor._format_prediction_output - Formatage sortie prédiction)
   - Lignes 6318-6378 dans hbp.py (61 lignes)
   - FONCTION : Formate résultat prédiction pour sortie standardisée avec métadonnées
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * combined_prediction (Dict) - Prédiction combinée
     * confidence (float) - Confiance finale
     * recommendation (str) - Recommandation générée
     * individual_predictions (List[Dict]) - Prédictions individuelles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STRUCTURE BASE :** Crée dictionnaire avec prédiction principale, confiance, recommandation
     * **MÉTADONNÉES :** Ajoute timestamp, version, configuration utilisée
     * **DÉTAILS MÉTHODES :** Inclut prédictions individuelles et poids appliqués
     * **STATISTIQUES :** Ajoute consensus, variance, incertitudes
     * **VALIDATION FORMAT :** Vérifie cohérence et complétude
     * **GESTION ERREURS :** Format minimal si erreur
   - RETOUR : Dict[str, Any] - Résultat formaté complet
   - UTILITÉ : Sortie standardisée avec toutes informations pertinentes

[Méthodes 10-26 d'UtilitairesFonctions : _update_prediction_history, _should_recommend, _validate_prediction_input, _handle_prediction_error, _get_prediction_metadata, _apply_prediction_filters, _post_process_prediction, _cache_prediction_result, _invalidate_prediction_cache, _get_cached_prediction, _prediction_confidence_boost, _prediction_uncertainty_analysis, _prediction_stability_check, _prediction_anomaly_detection, _prediction_trend_analysis, _prediction_pattern_recognition, _prediction_risk_assessment]

================================================================================
SECTION 8 : ANCIENNES CLASSES
================================================================================

1. class_HybridBaccaratPredictor.txt (Définition classe HybridBaccaratPredictor)
   - Lignes 1-1477 dans hbp.py (1477 lignes)
   - FONCTION : Définition classe principale avec initialisation complète et configuration
   - PARAMÈTRES :
     * config_path (str, optionnel) - Chemin fichier configuration
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Configure tous composants système (modèles, données, interface)
     * **CHARGEMENT CONFIG :** Lit configuration depuis fichier ou utilise défauts
     * **SETUP LOGGING :** Configure système logging avec niveaux et handlers
     * **INITIALISATION MODÈLES :** Prépare structures pour Markov, LGBM, LSTM
     * **SETUP DONNÉES :** Initialise structures données et caches
     * **CONFIGURATION POIDS :** Définit poids initiaux méthodes
   - RETOUR : Instance configurée de la classe
   - UTILITÉ : Point d'entrée principal système avec configuration complète

2. class_PredictorConfig.txt (Classe configuration PredictorConfig)
   - Lignes 10804-11000+ dans hbp.py (200+ lignes)
   - FONCTION : Classe configuration centralisée avec validation et gestion paramètres
   - PARAMÈTRES :
     * config_dict (Dict, optionnel) - Dictionnaire configuration personnalisée
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PARAMÈTRES DÉFAUT :** Définit valeurs par défaut tous paramètres système
     * **VALIDATION :** Valide cohérence et plages paramètres
     * **SÉRIALISATION :** Sauvegarde/chargement configuration
     * **MISE À JOUR :** Gestion modifications configuration runtime
   - RETOUR : Instance configuration validée
   - UTILITÉ : Gestion centralisée configuration avec validation et persistance
