DESCRIPTIF DÉTAILLÉ DES MÉTHODES - DEUXIÈME VAGUE TECHNIQUE
================================================================================

ÉTAT DOCUMENTATION : DEUXIÈME VAGUE COMPLÈTE
- **Couverture** : 100% des méthodes enrichies techniquement
- **Niveau** : Code source réel intégré, détails d'implémentation
- **Qualité** : 25-40 lignes par méthode avec précision technique

STRUCTURE DU SYSTÈME :
- **CalculConfiance** : Méthodes de calcul et gestion de la confiance (20 méthodes)
- **EvaluationMetriques** : Métriques de performance et évaluation (17 méthodes)
- **GestionDonnees** : Gestion des données et préparation (35 méthodes)
- **InterfaceUtilisateur** : Interface utilisateur et affichage (29 méthodes)
- **OptimisationEntrainement** : Optimisation et entraînement des modèles (32 méthodes)
- **ReseauxNeuronaux** : Réseaux de neurones et prédictions (3 méthodes)
- **UtilitairesFonctions** : Fonctions utilitaires et helpers (26 méthodes)
- **anciennesclasses** : Définitions de classes du système (2 classes)

TOTAL : 164 MÉTHODES ANALYSÉES

================================================================================
SECTION 1 : CALCUL CONFIANCE
================================================================================

1. calculate_confidence.txt (HybridBaccaratPredictor.calculate_confidence - Calcul confiance prédiction)
   - Lignes 9939-10055 dans hbp.py (117 lignes)
   - FONCTION : Calcule la confiance d'une prédiction en combinant incertitude épistémique, aléatoire et facteurs contextuels avec normalisation adaptative
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction (Dict[str, Any]) - Dictionnaire de prédiction contenant probabilités et incertitudes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PROBABILITÉS :** Récupère `prob_player = prediction.get('player', 0.5)` et `prob_banker = prediction.get('banker', 0.5)` avec fallback 0.5
     * **CALCUL ENTROPIE :** Utilise `entropy = -prob_player * np.log2(prob_player + 1e-10) - prob_banker * np.log2(prob_banker + 1e-10)` pour mesurer incertitude aléatoire
     * **NORMALISATION ENTROPIE :** Applique `normalized_entropy = entropy / np.log2(2)` pour borner entre [0,1]
     * **EXTRACTION INCERTITUDES :** Récupère `epistemic_uncertainty = prediction.get('epistemic_uncertainty', 0.5)` et `aleatoric_uncertainty = prediction.get('aleatoric_uncertainty', normalized_entropy)`
     * **CONFIANCE BASE :** Calcule `base_confidence = 1.0 - (0.6 * epistemic_uncertainty + 0.4 * aleatoric_uncertainty)` avec pondération 60/40
     * **FACTEUR SÉQUENCE :** Si `len(self.sequence) >= 10`, calcule bonus stabilité avec `recent_variance = np.var([1 if x == 'player' else 0 for x in self.sequence[-10:]])` puis `sequence_factor = 1.0 + 0.1 * (1.0 - 4 * recent_variance)`
     * **FACTEUR PERFORMANCE :** Utilise `recent_accuracy = self.get_recent_accuracy()` puis `performance_factor = 0.8 + 0.4 * recent_accuracy` pour ajustement basé historique
     * **CONFIANCE FINALE :** Combine avec `final_confidence = base_confidence * sequence_factor * performance_factor` puis borne avec `np.clip(final_confidence, 0.0, 1.0)`
     * **GESTION ERREURS :** Capture `Exception as e:` avec logging et retour `0.5` par défaut
   - RETOUR : float - Confiance normalisée entre 0.0 et 1.0
   - UTILITÉ : Évaluation sophistiquée de la fiabilité des prédictions avec facteurs multiples et normalisation robuste

2. get_recent_accuracy.txt (HybridBaccaratPredictor.get_recent_accuracy - Précision récente)
   - Lignes 10057-10088 dans hbp.py (32 lignes)
   - FONCTION : Calcule la précision sur les N dernières prédictions avec fenêtre glissante et gestion des cas limites
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * window_size (int, optionnel) - Taille de la fenêtre d'analyse (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION HISTORIQUE :** Vérifie `if not self.prediction_history:` avec retour `0.5` si aucune prédiction
     * **DÉTERMINATION FENÊTRE :** Calcule `actual_window = min(window_size, len(self.prediction_history))` pour adapter à l'historique disponible
     * **EXTRACTION RÉCENTE :** Utilise `recent_predictions = self.prediction_history[-actual_window:]` pour obtenir sous-ensemble
     * **COMPTAGE SUCCÈS :** Itère avec `for pred in recent_predictions:` et teste `if pred.get('prediction') == pred.get('actual_outcome'):` pour incrémenter `correct_count`
     * **CALCUL PRÉCISION :** Retourne `correct_count / actual_window` pour ratio succès/total
     * **GESTION ERREURS :** Capture `Exception as e:` avec logging et retour `0.5` par défaut
   - RETOUR : float - Précision entre 0.0 et 1.0
   - UTILITÉ : Mesure performance récente pour ajustement adaptatif de la confiance et des poids

3. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty - Calcul incertitude)
   - Lignes 10090-10178 dans hbp.py (89 lignes)
   - FONCTION : Calcule incertitude épistémique et aléatoire d'une prédiction avec analyse de désaccord entre modèles et entropie
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prediction (Dict[str, Any]) - Dictionnaire de prédiction avec probabilités des modèles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PRÉDICTIONS :** Récupère prédictions individuelles avec `markov_pred = prediction.get('markov', {'player': 0.5, 'banker': 0.5})` et similaire pour LGBM/LSTM
     * **CALCUL DÉSACCORD :** Pour chaque outcome, calcule variance avec `player_probs = [markov_pred['player'], lgbm_pred['player'], lstm_pred['player']]` puis `player_variance = np.var(player_probs)`
     * **INCERTITUDE ÉPISTÉMIQUE :** Utilise `epistemic_uncertainty = (player_variance + banker_variance) / 2` pour mesurer désaccord entre modèles
     * **EXTRACTION PROBABILITÉS FINALES :** Récupère `final_player = prediction.get('player', 0.5)` et `final_banker = prediction.get('banker', 0.5)`
     * **CALCUL ENTROPIE :** Applique `entropy = -final_player * np.log2(final_player + 1e-10) - final_banker * np.log2(final_banker + 1e-10)` pour incertitude aléatoire
     * **NORMALISATION ENTROPIE :** Utilise `aleatoric_uncertainty = entropy / np.log2(2)` pour borner [0,1]
     * **VALIDATION BORNES :** Applique `epistemic_uncertainty = np.clip(epistemic_uncertainty, 0.0, 1.0)` et similaire pour aléatoire
     * **GESTION ERREURS :** Capture exceptions avec retour `(0.5, 0.5)` par défaut
   - RETOUR : Tuple[float, float] - (incertitude_épistémique, incertitude_aléatoire)
   - UTILITÉ : Quantification précise de l'incertitude pour évaluation fiabilité et prise de décision

4. init_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.init_consecutive_confidence_calculator - Init calculateur confiance consécutive)
   - Lignes 1581-1587 dans hbp.py (7 lignes)
   - FONCTION : Initialise le calculateur de confiance pour recommandations consécutives avec protection contre réinitialisation multiple
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION DOUBLE INIT :** Vérifie `if hasattr(self, 'consecutive_confidence_calculator') and self.consecutive_confidence_calculator is not None:` avec retour si déjà initialisé
     * **IMPORT CLASSE :** Importe `from consecutive_confidence_calculator import ConsecutiveConfidenceCalculator` pour classe spécialisée
     * **INSTANCIATION :** Crée `self.consecutive_confidence_calculator = ConsecutiveConfidenceCalculator()` avec configuration par défaut
     * **LOGGING :** Enregistre `logger.info("Calculateur de confiance consécutive initialisé.")` pour traçabilité
   - RETOUR : None - Initialise directement l'attribut
   - UTILITÉ : Configuration paresseuse du calculateur spécialisé pour optimisation manches 31-60
